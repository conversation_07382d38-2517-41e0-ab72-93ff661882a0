package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 声音信息
 */
@Data
@NoArgsConstructor
public class VoiceInfo {
    
    /**
     * 声音ID
     */
    @JsonProperty("voiceId")
    private String voiceId;
    
    /**
     * 展示名称
     */
    @JsonProperty("displayName")
    private String displayName;
    
    /**
     * 语言
     */
    @JsonProperty("language")
    private String language;
    
    /**
     * 性别
     */
    @JsonProperty("gender")
    private String gender;
    
    /**
     * 试听音频文件URL
     */
    @JsonProperty("auditionFile")
    private String auditionFile;
    

    
    /**
     * 是否为中文声音
     */
    public boolean isChineseVoice() {
        return language != null && language.startsWith("zh");
    }
    
    /**
     * 是否为英文声音
     */
    public boolean isEnglishVoice() {
        return language != null && language.startsWith("en");
    }
    
    /**
     * 是否为男声
     */
    public boolean isMaleVoice() {
        return "男".equals(gender) || "Male".equalsIgnoreCase(gender);
    }
    
    /**
     * 是否为女声
     */
    public boolean isFemaleVoice() {
        return "女".equals(gender) || "Female".equalsIgnoreCase(gender);
    }
}