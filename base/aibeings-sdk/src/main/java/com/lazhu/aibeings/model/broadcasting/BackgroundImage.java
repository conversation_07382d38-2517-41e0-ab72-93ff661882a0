package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 背景图片配置
 */
public class BackgroundImage {
    
    /**
     * 图片URL
     */
    @JsonProperty("mediaUrl")
    private String mediaUrl;
    
    // Constructors
    public BackgroundImage() {}
    
    public BackgroundImage(String mediaUrl) {
        this.mediaUrl = mediaUrl;
    }
    
    // Getters and Setters
    public String getMediaUrl() {
        return mediaUrl;
    }
    
    public void setMediaUrl(String mediaUrl) {
        this.mediaUrl = mediaUrl;
    }
    
    /**
     * 验证背景图片配置
     */
    public boolean isValid() {
        return mediaUrl != null && !mediaUrl.trim().isEmpty();
    }
}