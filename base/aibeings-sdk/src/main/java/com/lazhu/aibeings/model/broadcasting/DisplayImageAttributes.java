package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 显示图片属性
 */
@Data
@NoArgsConstructor
public class DisplayImageAttributes {
    
    /**
     * 图片宽度
     */
    @JsonProperty("width")
    private Integer width;
    
    /**
     * 图片高度
     */
    @JsonProperty("height")
    private Integer height;
    
    /**
     * 图片坐标x值
     */
    @JsonProperty("x")
    private Integer x;
    
    /**
     * 图片坐标y值
     */
    @JsonProperty("y")
    private Integer y;
    
    /**
     * 透明度，取值范围0-1，默认为1
     */
    @JsonProperty("opacity")
    private Double opacity = 1.0;
    
    /**
     * 角度，取值0，90，180，270。默认0
     */
    @JsonProperty("rotation")
    private Integer rotation = 0;
    
    // 便捷构造函数
    public DisplayImageAttributes(Integer width, Integer height, Integer x, Integer y) {
        this.width = width;
        this.height = height;
        this.x = x;
        this.y = y;
    }
    
    /**
     * 设置透明度
     */
    public DisplayImageAttributes withOpacity(double opacity) {
        if (opacity >= 0.0 && opacity <= 1.0) {
            this.opacity = opacity;
        }
        return this;
    }
    
    /**
     * 设置旋转角度
     */
    public DisplayImageAttributes withRotation(int rotation) {
        if (rotation == 0 || rotation == 90 || rotation == 180 || rotation == 270) {
            this.rotation = rotation;
        }
        return this;
    }
    
    /**
     * 创建居中位置的图片属性
     */
    public static DisplayImageAttributes centered(int videoWidth, int videoHeight, 
                                                 int imageWidth, int imageHeight) {
        int x = (videoWidth - imageWidth) / 2;
        int y = (videoHeight - imageHeight) / 2;
        return new DisplayImageAttributes(imageWidth, imageHeight, x, y);
    }
    
    /**
     * 验证图片属性
     */
    public boolean isValid() {
        if (width == null || height == null || x == null || y == null) {
            return false;
        }
        if (width <= 0 || height <= 0 || x < 0 || y < 0) {
            return false;
        }
        if (opacity != null && (opacity < 0.0 || opacity > 1.0)) {
            return false;
        }
        if (rotation != null && rotation != 0 && rotation != 90 && rotation != 180 && rotation != 270) {
            return false;
        }
        return true;
    }
}