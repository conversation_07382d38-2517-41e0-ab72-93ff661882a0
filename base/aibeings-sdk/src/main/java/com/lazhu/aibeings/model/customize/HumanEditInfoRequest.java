package com.lazhu.aibeings.model.customize;

import com.lazhu.aibeings.model.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 入驻前配置数字人请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class HumanEditInfoRequest extends BaseRequest {
    
    /**
     * 任务id，在创建形象训练任务时的返回结果中获取
     */
    private String taskId;
    
    /**
     * 数字人名称
     */
    private String name;
    
    /**
     * 声音bizId，在获取声音训练结果的返回结果中获取
     */
    private String bizId;
    
    // 便捷构造函数
    public HumanEditInfoRequest(String taskId, String name, String bizId) {
        this.taskId = taskId;
        this.name = name;
        this.bizId = bizId;
    }
}