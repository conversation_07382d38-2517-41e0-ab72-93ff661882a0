package com.lazhu.aibeings.config;

/**
 * AI Beings VIP Platform Configuration
 * 
 * <AUTHOR> SDK Generator
 * @version 1.0.0
 */
public class AIBeingsConfig {
    
    private String baseUrl = "https://aibeings-vip.xiaoice.com";
    private String subscriptionKey;
    private int connectTimeout = 30000; // 30 seconds
    private int readTimeout = 60000; // 60 seconds
    private int maxRetries = 3;
    
    public AIBeingsConfig() {}
    
    public AIBeingsConfig(String subscriptionKey) {
        this.subscriptionKey = subscriptionKey;
    }
    
    public AIBeingsConfig(String baseUrl, String subscriptionKey) {
        this.baseUrl = baseUrl;
        this.subscriptionKey = subscriptionKey;
    }
    
    // Getters and Setters
    public String getBaseUrl() {
        return baseUrl;
    }
    
    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }
    
    public String getSubscriptionKey() {
        return subscriptionKey;
    }
    
    public void setSubscriptionKey(String subscriptionKey) {
        this.subscriptionKey = subscriptionKey;
    }
    
    public int getConnectTimeout() {
        return connectTimeout;
    }
    
    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }
    
    public int getReadTimeout() {
        return readTimeout;
    }
    
    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
    
    public int getMaxRetries() {
        return maxRetries;
    }
    
    public void setMaxRetries(int maxRetries) {
        this.maxRetries = maxRetries;
    }
}