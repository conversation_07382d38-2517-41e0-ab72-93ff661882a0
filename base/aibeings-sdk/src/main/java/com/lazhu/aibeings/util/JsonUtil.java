package com.lazhu.aibeings.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.lazhu.aibeings.exception.AIBeingsException;

/**
 * JSON utility class for AI Beings SDK
 */
public class JsonUtil {
    
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    
    static {
        // Configure ObjectMapper
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        OBJECT_MAPPER.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
    }
    
    /**
     * Convert object to JSON string
     * 
     * @param obj Object to convert
     * @return JSON string
     */
    public static String toJson(Object obj) {
        try {
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (Exception e) {
            throw new AIBeingsException("Failed to convert object to JSON: " + e.getMessage(), e);
        }
    }
    
    /**
     * Convert JSON string to object
     * 
     * @param json JSON string
     * @param clazz Target class
     * @param <T> Target type
     * @return Converted object
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (Exception e) {
            throw new AIBeingsException("Failed to convert JSON to object: " + e.getMessage(), e);
        }
    }
    
    /**
     * Get ObjectMapper instance
     * 
     * @return ObjectMapper instance
     */
    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER;
    }
}