package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 显示文本属性
 */
@Data
@NoArgsConstructor
public class DisplayTextAttributes {
    
    /**
     * 坐标x值
     */
    @JsonProperty("x")
    private Integer x;
    
    /**
     * 坐标y值
     */
    @JsonProperty("y")
    private Integer y;
    
    /**
     * 字体
     */
    @JsonProperty("font")
    private Integer font;
    
    /**
     * 字号
     */
    @JsonProperty("fontSize")
    private Integer fontSize;
    
    /**
     * 字体颜色
     */
    @JsonProperty("fontColor")
    private String fontColor;
    
    /**
     * 字体透明度，取值范围0-1
     */
    @JsonProperty("fontColorOpacity")
    private Double fontColorOpacity = 1.0;
    
    /**
     * 角度
     */
    @JsonProperty("angle")
    private Integer angle = 0;
    
    /**
     * 加粗
     */
    @JsonProperty("bold")
    private Boolean bold = false;
    
    /**
     * 斜体
     */
    @JsonProperty("italic")
    private Boolean italic = false;
    
    /**
     * 下划线
     */
    @JsonProperty("underline")
    private Boolean underline = false;
    
    /**
     * 字间距
     */
    @JsonProperty("spacing")
    private Integer spacing = 0;
    
    /**
     * 字体源地址
     */
    @JsonProperty("fontPath")
    private String fontPath;
    
    /**
     * 是否开启阴影
     */
    @JsonProperty("shadowOn")
    private Boolean shadowOn = false;
    
    /**
     * 阴影颜色
     */
    @JsonProperty("shadowColor")
    private String shadowColor;
    
    /**
     * 阴影偏移水平距离
     */
    @JsonProperty("shadowOffsetX")
    private Integer shadowOffsetX;
    
    /**
     * 阴影偏移垂直距离
     */
    @JsonProperty("shadowOffsetY")
    private Integer shadowOffsetY;
    
    /**
     * 是否开启描边
     */
    @JsonProperty("strokeOn")
    private Boolean strokeOn = false;
    
    /**
     * 字体描边颜色
     */
    @JsonProperty("strokeColor")
    private String strokeColor;
    
    /**
     * 字体描边宽度
     */
    @JsonProperty("strokeWidth")
    private Float strokeWidth;
    
    /**
     * 背景颜色
     */
    @JsonProperty("backgroundColor")
    private String backgroundColor;
    
    /**
     * 背景边框圆弧的大小
     */
    @JsonProperty("radius")
    private Integer radius;
    
    // 便捷构造函数
    public DisplayTextAttributes(Integer x, Integer y, Integer font, Integer fontSize, String fontColor) {
        this.x = x;
        this.y = y;
        this.font = font;
        this.fontSize = fontSize;
        this.fontColor = fontColor;
    }
    
    /**
     * 设置阴影效果
     */
    public DisplayTextAttributes withShadow(String color, int offsetX, int offsetY) {
        this.shadowOn = true;
        this.shadowColor = color;
        this.shadowOffsetX = offsetX;
        this.shadowOffsetY = offsetY;
        return this;
    }
    
    /**
     * 设置描边效果
     */
    public DisplayTextAttributes withStroke(String color, float width) {
        this.strokeOn = true;
        this.strokeColor = color;
        this.strokeWidth = width;
        return this;
    }
    
    /**
     * 设置背景
     */
    public DisplayTextAttributes withBackground(String color, int radius) {
        this.backgroundColor = color;
        this.radius = radius;
        return this;
    }
    
    /**
     * 验证文本属性
     */
    public boolean isValid() {
        if (font == null || fontSize == null || fontColor == null) {
            return false;
        }
        if (fontSize < 1 || fontSize > 200) {
            return false;
        }
        if (angle != null && angle != 0 && angle != 90 && angle != 180 && angle != 270) {
            return false;
        }
        if (fontColorOpacity != null && (fontColorOpacity < 0.0 || fontColorOpacity > 1.0)) {
            return false;
        }
        return true;
    }
}