package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lazhu.aibeings.model.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * TTS音频生成请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class TTSAudioRequest extends BaseRequest {
    
    /**
     * 要合成的文本
     */
    @JsonProperty("text")
    private String text;
    
    /**
     * 声音ID
     */
    @JsonProperty("voiceId")
    private String voiceId;
    
    /**
     * 语速，默认1，取值范围 >=0.6, <=1.5
     */
    @JsonProperty("rate")
    private Float rate = 1.0f;
    
    /**
     * 语调，默认1，取值范围 >=0.6, <=1.5
     */
    @JsonProperty("pitch")
    private Float pitch = 1.0f;
    
    /**
     * 音量，默认50，取值范围 >=0 <=100
     */
    @JsonProperty("volume")
    private Integer volume = 50;
    
    /**
     * 音频格式，支持mp3、wav，默认mp3
     */
    @JsonProperty("format")
    private String format = "mp3";
    
    // 便捷构造函数
    public TTSAudioRequest(String text, String voiceId) {
        this.text = text;
        this.voiceId = voiceId;
    }
    
    public TTSAudioRequest(String text, String voiceId, Float rate, Float pitch, Integer volume) {
        this.text = text;
        this.voiceId = voiceId;
        this.rate = rate;
        this.pitch = pitch;
        this.volume = volume;
    }
    
    /**
     * 设置语速
     */
    public TTSAudioRequest withRate(float rate) {
        if (rate >= 0.6f && rate <= 1.5f) {
            this.rate = rate;
        }
        return this;
    }
    
    /**
     * 设置语调
     */
    public TTSAudioRequest withPitch(float pitch) {
        if (pitch >= 0.6f && pitch <= 1.5f) {
            this.pitch = pitch;
        }
        return this;
    }
    
    /**
     * 设置音量
     */
    public TTSAudioRequest withVolume(int volume) {
        if (volume >= 0 && volume <= 100) {
            this.volume = volume;
        }
        return this;
    }
    
    /**
     * 设置音频格式
     */
    public TTSAudioRequest withFormat(String format) {
        if ("mp3".equalsIgnoreCase(format) || "wav".equalsIgnoreCase(format)) {
            this.format = format.toLowerCase();
        }
        return this;
    }
    
    /**
     * 验证请求参数
     */
    public boolean isValid() {
        if (text == null || text.trim().isEmpty()) {
            return false;
        }
        if (voiceId == null || voiceId.trim().isEmpty()) {
            return false;
        }
        if (rate != null && (rate < 0.6f || rate > 1.5f)) {
            return false;
        }
        if (pitch != null && (pitch < 0.6f || pitch > 1.5f)) {
            return false;
        }
        if (volume != null && (volume < 0 || volume > 100)) {
            return false;
        }
        return true;
    }
}