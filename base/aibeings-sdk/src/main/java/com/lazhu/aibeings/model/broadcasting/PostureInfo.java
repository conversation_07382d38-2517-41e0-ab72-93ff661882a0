package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 动作信息
 */
@Data
@NoArgsConstructor
public class PostureInfo {
    
    /**
     * 动作ID
     */
    @JsonProperty("bizId")
    private String bizId;
    
    /**
     * 动作名称
     */
    @JsonProperty("name")
    private String name;
    
    /**
     * 动作预览图URL
     */
    @JsonProperty("previewPicture")
    private String previewPicture;
    

}