package com.lazhu.aibeings.model.customize;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 声音检测结果
 */
@Data
@NoArgsConstructor
public class VoiceCheckResult {
    
    /**
     * 音频文件
     */
    private String audio;
    
    /**
     * 噪音级别 <=50为合格音频
     */
    private Float nrLevel;
    
    /**
     * 状态码 200为成功，其他为失败
     */
    private Integer status;
    
    /**
     * 音量，不做强制校验，建议30-80之间
     */
    private Float volumn;
    

    
    /**
     * 是否检测成功
     */
    public boolean isSuccess() {
        return status != null && status == 200;
    }
    
    /**
     * 噪音级别是否合格
     */
    public boolean isNoiseQualified() {
        return nrLevel != null && nrLevel <= 50.0f;
    }
    
    /**
     * 音量是否在建议范围内
     */
    public boolean isVolumeRecommended() {
        return volumn != null && volumn >= 30.0f && volumn <= 80.0f;
    }
    
    @Override
    public String toString() {
        return "VoiceCheckResult{" +
                "audio='" + audio + '\'' +
                ", nrLevel=" + nrLevel +
                ", status=" + status +
                ", volumn=" + volumn +
                '}';
    }
}