package com.lazhu.aibeings.model.customize;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 启动训练响应
 */
@Data
@NoArgsConstructor
public class StartTrainingRes {
    
    /**
     * 启动成功、失败
     */
    private Boolean result;
    
    /**
     * 预估耗时（分钟）
     */
    private Integer estimateMinutes;
    
    // 便捷构造函数
    public StartTrainingRes(Boolean result, Integer estimateMinutes) {
        this.result = result;
        this.estimateMinutes = estimateMinutes;
    }
    
    /**
     * 是否启动成功
     */
    public boolean isSuccess() {
        return result != null && result;
    }
    
    @Override
    public String toString() {
        return "StartTrainingRes{" +
                "result=" + result +
                ", estimateMinutes=" + estimateMinutes +
                '}';
    }
}