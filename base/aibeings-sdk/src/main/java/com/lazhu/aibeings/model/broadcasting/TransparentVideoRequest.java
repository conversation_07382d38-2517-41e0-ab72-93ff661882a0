package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lazhu.aibeings.model.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * 透明视频生成请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class TransparentVideoRequest extends BaseRequest {
    
    /**
     * 输出视频名称
     */
    @JsonProperty("outputVideoName")
    private String outputVideoName;
    
    /**
     * 虚拟人配置
     */
    @JsonProperty("virtualHuman")
    private VirtualHuman virtualHuman;
    
    /**
     * TTS配置
     */
    @JsonProperty("tts")
    private TTSConfig tts;
    
    /**
     * 自定义语音的音频URL
     */
    @JsonProperty("voiceUrl")
    private String voiceUrl;
    
    /**
     * 播报文本列表
     */
    @JsonProperty("voiceTexts")
    private List<String> voiceTexts;
    
    /**
     * 视频宽度
     */
    @JsonProperty("width")
    private Integer width = 1920;
    
    /**
     * 视频高度
     */
    @JsonProperty("height")
    private Integer height = 1080;
    
    /**
     * 输出视频格式
     */
    @JsonProperty("outputVideoFormat")
    private String outputVideoFormat = "mp4";
    
    // 便捷构造函数
    public TransparentVideoRequest(String outputVideoName, VirtualHuman virtualHuman) {
        this.outputVideoName = outputVideoName;
        this.virtualHuman = virtualHuman;
    }
    
    /**
     * 使用TTS驱动
     */
    public TransparentVideoRequest withTTS(String voiceId, List<String> texts) {
        this.tts = new TTSConfig(voiceId);
        this.voiceTexts = texts;
        return this;
    }
    
    /**
     * 使用音频URL驱动
     */
    public TransparentVideoRequest withVoiceUrl(String voiceUrl) {
        this.voiceUrl = voiceUrl;
        return this;
    }
    
    /**
     * 设置视频尺寸
     */
    public TransparentVideoRequest withDimensions(int width, int height) {
        this.width = width;
        this.height = height;
        return this;
    }
    
    /**
     * 验证请求参数
     */
    public boolean isValid() {
        if (outputVideoName == null || outputVideoName.trim().isEmpty()) {
            return false;
        }
        if (virtualHuman == null || !virtualHuman.isValid()) {
            return false;
        }
        
        // TTS和音频URL二选一
        boolean hasTTS = tts != null && tts.isValid() && voiceTexts != null && !voiceTexts.isEmpty();
        boolean hasVoiceUrl = voiceUrl != null && !voiceUrl.trim().isEmpty();
        
        return hasTTS || hasVoiceUrl;
    }
}