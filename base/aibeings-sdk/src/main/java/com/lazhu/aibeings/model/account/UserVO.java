package com.lazhu.aibeings.model.account;

import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户信息对象
 */
@Data
@NoArgsConstructor
public class UserVO {
    
    /**
     * 用户ID
     */
    private String id;
    
    /**
     * 用户姓名
     */
    private String displayName;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 所属组织ID
     */
    private Integer orgId;
    
    /**
     * 角色列表
     */
    private List<String> role;
    
    /**
     * 账号状态 (true:禁用、false:启用)
     */
    private Boolean lockout;
    

    
    /**
     * 是否为管理员
     */
    public boolean isAdmin() {
        return role != null && role.contains("admin");
    }
    
    /**
     * 是否为普通用户
     */
    public boolean isUser() {
        return role != null && role.contains("user");
    }
    
    /**
     * 是否被禁用
     */
    public boolean isLocked() {
        return lockout != null && lockout;
    }
    
    /**
     * 是否启用
     */
    public boolean isEnabled() {
        return lockout == null || !lockout;
    }
    
}