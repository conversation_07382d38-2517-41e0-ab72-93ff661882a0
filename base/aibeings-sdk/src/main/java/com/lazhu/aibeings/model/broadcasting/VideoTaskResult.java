package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 视频任务结果
 */
@Data
@NoArgsConstructor
public class VideoTaskResult {
    
    /**
     * 任务ID
     */
    @JsonProperty("id")
    private String id;
    
    /**
     * 视频名称
     */
    @JsonProperty("videoName")
    private String videoName;
    
    /**
     * 任务创建时间
     */
    @JsonProperty("createTime")
    private String createTime;
    
    /**
     * 失败消息
     */
    @JsonProperty("error")
    private String error;
    
    /**
     * 任务完成时间
     */
    @JsonProperty("finishTime")
    private String finishTime;
    
    /**
     * 任务状态
     * running-创建中、finished-任务完成、error-任务失败
     */
    @JsonProperty("status")
    private String status;
    
    /**
     * 进度
     */
    @JsonProperty("progress")
    private Double progress;
    
    /**
     * 视频时长（毫秒）
     */
    @JsonProperty("videoDurationMilliseconds")
    private Long videoDurationMilliseconds;
    
    /**
     * 视频封面图
     */
    @JsonProperty("videoThumbnailImageUrl")
    private String videoThumbnailImageUrl;
    
    /**
     * 视频地址
     */
    @JsonProperty("videoUrl")
    private String videoUrl;
    
    /**
     * 视频大小（字节）
     */
    @JsonProperty("videoSizeBytes")
    private Long videoSizeBytes;
    
    /**
     * 任务来源
     */
    @JsonProperty("clientType")
    private String clientType;
    
    // 状态常量
    public static final String STATUS_RUNNING = "running";
    public static final String STATUS_FINISHED = "finished";
    public static final String STATUS_ERROR = "error";
    

    
    /**
     * 是否正在运行
     */
    public boolean isRunning() {
        return STATUS_RUNNING.equals(status);
    }
    
    /**
     * 是否已完成
     */
    public boolean isFinished() {
        return STATUS_FINISHED.equals(status);
    }
    
    /**
     * 是否失败
     */
    public boolean isError() {
        return STATUS_ERROR.equals(status);
    }
    
    /**
     * 获取视频时长（秒）
     */
    public Double getVideoDurationSeconds() {
        if (videoDurationMilliseconds == null) {
            return null;
        }
        return videoDurationMilliseconds / 1000.0;
    }
    
    /**
     * 获取视频大小（MB）
     */
    public Double getVideoSizeMB() {
        if (videoSizeBytes == null) {
            return null;
        }
        return videoSizeBytes / (1024.0 * 1024.0);
    }
    
    /**
     * 获取进度百分比
     */
    public Integer getProgressPercentage() {
        if (progress == null) {
            return null;
        }
        return (int) (progress * 100);
    }
}