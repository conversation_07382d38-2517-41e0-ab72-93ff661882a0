package com.lazhu.aibeings.model.customize;

import com.lazhu.aibeings.model.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 启动训练请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class StartTrainingRequest extends BaseRequest {
    
    /**
     * 定制任务的bizId，在创建零样本形象定制任务时的返回结果中获取
     */
    private String bizId;
    
    /**
     * 数字人名字，最大长度16字符
     */
    private String name;
    
    /**
     * 声音bizId
     * 若本次同时定制零样本形象和零样本声音，则不传该字段；
     * 若本次仅定制零样本形象，则传入该形象配置的声音bizId，可在获取声音训练结果的返回结果中获取
     */
    private String voiceBizId;
    
    /**
     * 本次同时定制零样本形象和零样本声音时，此处需指定语言类型
     * CHINESE 普通话，MULTI 多语言，默认为普通话
     */
    private String languageType;
    
    /**
     * 若本次定制零样本多语种声音，此处可指定是否消除背景噪音
     * 0 否，1 是；默认 否
     */
    private Integer reduceNoise;
    
    // 便捷构造函数
    public StartTrainingRequest(String bizId, String name) {
        this.bizId = bizId;
        this.name = name;
    }
    
    /**
     * 语言类型枚举
     */
    public enum LanguageType {
        CHINESE("CHINESE", "普通话"),
        MULTI("MULTI", "多语言");
        
        private final String code;
        private final String description;
        
        LanguageType(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    @Override
    public String toString() {
        return "StartTrainingRequest{" +
                "bizId='" + bizId + '\'' +
                ", name='" + name + '\'' +
                ", voiceBizId='" + voiceBizId + '\'' +
                ", languageType='" + languageType + '\'' +
                ", reduceNoise=" + reduceNoise +
                '}';
    }
}