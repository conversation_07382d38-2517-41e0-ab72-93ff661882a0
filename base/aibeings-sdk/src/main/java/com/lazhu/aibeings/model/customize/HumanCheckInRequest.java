package com.lazhu.aibeings.model.customize;

import com.lazhu.aibeings.model.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 数字人入驻/取消入驻请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class HumanCheckInRequest extends BaseRequest {
    
    /**
     * 任务id，在创建形象训练任务时的返回结果中获取
     */
    private String taskId;
    
    /**
     * true: 入驻 false: 取消入驻
     */
    private Boolean checkIn;
    
    // 便捷构造函数
    public HumanCheckInRequest(String taskId, Boolean checkIn) {
        this.taskId = taskId;
        this.checkIn = checkIn;
    }
}