package com.lazhu.aibeings.client;

import com.lazhu.aibeings.model.BaseResponse;
import com.lazhu.aibeings.model.broadcasting.*;
import com.lazhu.aibeings.util.JsonUtil;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数字人播报客户端
 * 提供数字人播报相关的API接口
 */
public class BroadcastingClient {
    
    private final HttpClient httpClient;
    
    public BroadcastingClient(HttpClient httpClient) {
        this.httpClient = httpClient;
    }
    
    /**
     * 查询播报数字员工详情
     * 
     * @param bizId 数字员工bizId
     * @return 数字员工详情
     */
    public BaseResponse<DigitalEmployeeDetail> getDigitalEmployeeDetail(String bizId) {
        String url = "/openapi/video/detailDigitalEmployee";
        
        DigitalEmployeeDetailRequest request = new DigitalEmployeeDetailRequest(bizId);
        
        return httpClient.post(url, request, DigitalEmployeeDetail.class);
    }
    
    /**
     * 创建编辑器模式视频生成任务
     * 
     * @param request 视频生成请求
     * @return 任务ID
     */
    public BaseResponse<VideoTaskResponse> createEditorModeVideo(VideoCreationRequest request) {
        String url = "/openapi/video/task/v2/submit";
        return httpClient.post(url, request, VideoTaskResponse.class);
    }
    
    /**
     * 查询视频生成任务结果
     * 
     * @param taskId 任务ID
     * @return 任务结果
     */
    public BaseResponse<VideoTaskResult> getVideoTaskResult(String taskId) {
        String url = "/openapi/video/task/v2/detail?taskId=" + taskId;
        Map<String, Object> params = new HashMap<>();
        return httpClient.get(url, params, VideoTaskResult.class);
    }
    
    /**
     * 删除视频生成任务
     * 
     * @param taskIds 任务ID列表
     * @return 删除结果
     */
    public BaseResponse<Void> deleteVideoTasks(List<String> taskIds) {
        String url = "/openapi/video/task/v2/remove";
        
        DeleteVideoTasksRequest request = new DeleteVideoTasksRequest(taskIds);
        
        return httpClient.post(url, request, Void.class);
    }
    
    /**
     * 查询视频任务列表
     * 
     * @param request 查询请求
     * @return 任务列表
     */
    public BaseResponse<VideoTaskPageResponse> queryVideoTasks(VideoTaskQueryRequest request) {
        String url = "/openapi/video/task/v2/pageQuery";
        return httpClient.post(url, request, VideoTaskPageResponse.class);
    }
    
    /**
     * 生成PPT讲解视频
     * 
     * @param request PPT视频生成请求
     * @return 任务ID
     */
    public BaseResponse<VideoTaskResponse> createPPTVideo(PPTVideoRequest request) {
        String url = "/openapi/video/ppt/submit";
        return httpClient.post(url, request, VideoTaskResponse.class);
    }
    
    /**
     * 生成纯数字人透明视频
     * 
     * @param request 透明视频生成请求
     * @return 任务ID
     */
    public BaseResponse<VideoTaskResponse> createTransparentVideo(TransparentVideoRequest request) {
        String url = "/openapi/video/transparent/submit";
        return httpClient.post(url, request, VideoTaskResponse.class);
    }
    
    /**
     * 生成TTS音频
     * 
     * @param request TTS音频生成请求
     * @return 音频结果
     */
    public BaseResponse<TTSAudioResponse> generateTTSAudio(TTSAudioRequest request) {
        String url = "/openapi/audio/tts/generate";
        return httpClient.post(url, request, TTSAudioResponse.class);
    }
}