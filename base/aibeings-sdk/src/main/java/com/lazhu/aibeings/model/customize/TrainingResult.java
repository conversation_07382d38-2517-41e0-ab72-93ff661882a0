package com.lazhu.aibeings.model.customize;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 训练结果
 */
@Data
@NoArgsConstructor
public class TrainingResult {
    
    /**
     * -1(失败),0(进行中),1(完成)
     */
    private Integer status;
    
    /**
     * 进度
     */
    private Integer progress;
    
    /**
     * 失败原因
     */
    private String message;
    
    /**
     * 数字人bizId
     */
    private String bizId;
    
    /**
     * 用于配置数字人的声音bizId (仅声音训练)
     */
    private String voiceBizId;
    
    /**
     * 用于合成播报视频、音频的声音ID (仅声音训练)
     */
    private String voiceOpenapiId;
    
    /**
     * 声音示例url (仅零样本声音训练)
     */
    private String auditionFile;
    

    
    /**
     * 状态枚举
     */
    public enum Status {
        FAILED(-1, "失败"),
        IN_PROGRESS(0, "进行中"),
        COMPLETED(1, "完成");
        
        private final int code;
        private final String description;
        
        Status(int code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public int getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 是否完成
     */
    public boolean isCompleted() {
        return status != null && status == Status.COMPLETED.getCode();
    }
    
    /**
     * 是否失败
     */
    public boolean isFailed() {
        return status != null && status == Status.FAILED.getCode();
    }
    
    /**
     * 是否进行中
     */
    public boolean isInProgress() {
        return status != null && status == Status.IN_PROGRESS.getCode();
    }
    
    @Override
    public String toString() {
        return "TrainingResult{" +
                "status=" + status +
                ", progress=" + progress +
                ", message='" + message + '\'' +
                ", bizId='" + bizId + '\'' +
                ", voiceBizId='" + voiceBizId + '\'' +
                ", voiceOpenapiId='" + voiceOpenapiId + '\'' +
                ", auditionFile='" + auditionFile + '\'' +
                '}';
    }
}