package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lazhu.aibeings.model.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 视频任务查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class VideoTaskQueryRequest extends BaseRequest {
    
    /**
     * 分页页码
     */
    @JsonProperty("pageIndex")
    private Integer pageIndex;
    
    /**
     * 分页大小
     */
    @JsonProperty("pageSize")
    private Integer pageSize;
    
    /**
     * 用户ID
     */
    @JsonProperty("userId")
    private String userId;
    
    /**
     * 用户邮箱
     */
    @JsonProperty("userEmail")
    private String userEmail;
    
    /**
     * 用户手机号
     */
    @JsonProperty("userPhone")
    private String userPhone;
    
    // 便捷构造函数
    public VideoTaskQueryRequest(Integer pageIndex, Integer pageSize) {
        this.pageIndex = pageIndex;
        this.pageSize = pageSize;
    }
    
    /**
     * 设置用户ID查询
     */
    public VideoTaskQueryRequest withUserId(String userId) {
        this.userId = userId;
        return this;
    }
    
    /**
     * 设置用户邮箱查询
     */
    public VideoTaskQueryRequest withUserEmail(String userEmail) {
        this.userEmail = userEmail;
        return this;
    }
    
    /**
     * 设置用户手机号查询
     */
    public VideoTaskQueryRequest withUserPhone(String userPhone) {
        this.userPhone = userPhone;
        return this;
    }
}