package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * 视频任务分页响应
 */
@Data
@NoArgsConstructor
public class VideoTaskPageResponse {
    
    /**
     * 当前页码
     */
    @JsonProperty("current")
    private Integer current;
    
    /**
     * 页大小
     */
    @JsonProperty("size")
    private Integer size;
    
    /**
     * 总条数
     */
    @JsonProperty("total")
    private Integer total;
    
    /**
     * 总页数
     */
    @JsonProperty("pages")
    private Integer pages;
    
    /**
     * 任务列表
     */
    @JsonProperty("records")
    private List<VideoTaskResult> records;
    

    
    /**
     * 是否有下一页
     */
    public boolean hasNext() {
        return current != null && pages != null && current < pages;
    }
    
    /**
     * 是否有上一页
     */
    public boolean hasPrevious() {
        return current != null && current > 1;
    }
    
    /**
     * 是否为空
     */
    public boolean isEmpty() {
        return records == null || records.isEmpty();
    }
    
    /**
     * 获取记录数量
     */
    public int getRecordCount() {
        return records == null ? 0 : records.size();
    }
}