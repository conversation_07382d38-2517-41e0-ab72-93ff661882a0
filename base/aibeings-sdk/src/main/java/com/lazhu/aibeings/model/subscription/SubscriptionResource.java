package com.lazhu.aibeings.model.subscription;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订阅资源信息模型
 */
@Data
@NoArgsConstructor
public class SubscriptionResource {
    
    @JsonProperty("code")
    private String code;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("readableQuantity")
    private Long readableQuantity;
    
    @JsonProperty("readableUnit")
    private String readableUnit;
    

    
    /**
     * 获取可读的数量显示
     */
    public String getReadableQuantityDisplay() {
        return (readableQuantity != null ? readableQuantity : 0) + " " + 
               (readableUnit != null ? readableUnit : "");
    }
}