package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 背景音乐配置
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BackgroundMusic {
    
    /**
     * 背景音乐URL
     */
    @JsonProperty("mediaUrl")
    private String mediaUrl;
    
    /**
     * 音量取值范围0-1
     */
    @JsonProperty("volume")
    private Double volume = 1.0;
    
    /**
     * 倍速取值范围0-3
     */
    @JsonProperty("speed")
    private Double speed = 1.0;
    
    /**
     * 是否循环播放
     */
    @JsonProperty("loop")
    private Boolean loop = true;
    
    // 便捷构造函数
    public BackgroundMusic(String mediaUrl) {
        this.mediaUrl = mediaUrl;
    }
    
    public BackgroundMusic(String mediaUrl, Double volume, Boolean loop) {
        this.mediaUrl = mediaUrl;
        this.volume = volume;
        this.loop = loop;
    }
    
    /**
     * 设置音量
     */
    public BackgroundMusic withVolume(double volume) {
        if (volume >= 0.0 && volume <= 1.0) {
            this.volume = volume;
        }
        return this;
    }
    
    /**
     * 设置倍速
     */
    public BackgroundMusic withSpeed(double speed) {
        if (speed >= 0.0 && speed <= 3.0) {
            this.speed = speed;
        }
        return this;
    }
    
    /**
     * 设置循环播放
     */
    public BackgroundMusic withLoop(boolean loop) {
        this.loop = loop;
        return this;
    }
    
    /**
     * 验证背景音乐配置
     */
    public boolean isValid() {
        if (mediaUrl == null || mediaUrl.trim().isEmpty()) {
            return false;
        }
        if (volume != null && (volume < 0.0 || volume > 1.0)) {
            return false;
        }
        if (speed != null && (speed < 0.0 || speed > 3.0)) {
            return false;
        }
        return true;
    }
}