package com.lazhu.aibeings.example;

import com.lazhu.aibeings.AIBeingsClient;
import com.lazhu.aibeings.client.CustomizationClient;
import com.lazhu.aibeings.model.BaseResponse;
import com.lazhu.aibeings.model.customize.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * 定制数字人示例
 * 
 * 演示如何使用CustomizationClient进行数字人定制，包括：
 * - 高级定制形象：上传3～6分钟视频，定制高级播报、交互形象
 * - 高级定制声音：上传5～12分钟音频，定制高级声音
 * - 极速定制形象：分钟级生成播报分身，小时级尊享交互体验
 * - 极速定制声音：秒级复刻声音
 */
public class CustomizationExample {
    
    private static final Logger logger = LoggerFactory.getLogger(CustomizationExample.class);
    
    public static void main(String[] args) {
        // 替换为您的实际subscription key
        String subscriptionKey = "your-subscription-key-here";
        
        // 创建客户端
        AIBeingsClient client = new AIBeingsClient(subscriptionKey);
        CustomizationClient customizationClient = client.customization();
        
        try {
            // 示例1: 高级定制形象
            advancedImageCustomizationExample(customizationClient);
            
            // 示例2: 高级定制声音
            advancedVoiceCustomizationExample(customizationClient);
            
            // 示例3: 极速定制形象
            zeroShotImageCustomizationExample(customizationClient);
            
            // 示例4: 极速定制声音
            zeroShotVoiceCustomizationExample(customizationClient);
            
        } catch (Exception e) {
            logger.error("Customization example failed: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 高级定制形象示例
     */
    private static void advancedImageCustomizationExample(CustomizationClient client) {
        logger.info("=== 高级定制形象示例 ===");
        
        try {
            // 1. 创建形象训练任务
            String videoUrl = "https://example.com/training-video.mp4";
            BaseResponse<String> submitResponse = client.submitStudioVideoTraining(videoUrl);
            
            if (!submitResponse.isSuccess()) {
                logger.error("提交形象训练任务失败: {}", submitResponse.getMessage());
                return;
            }
            
            String taskId = submitResponse.getData();
            logger.info("成功提交形象训练任务，任务ID: {}", taskId);
            
            // 2. 轮询获取训练结果
            VideoTrainingTaskResult result = pollVideoTrainingResult(client, taskId);
            if (result == null) {
                return;
            }
            
            // 3. 如果需要确认抠像效果
            if (result.needsPreviewConfirmation()) {
                logger.info("需要确认抠像效果，预览视频数量: {}", 
                           result.getPreviewVideoList() != null ? result.getPreviewVideoList().size() : 0);
                
                // 显示预览视频信息
                if (result.getPreviewVideoList() != null) {
                    for (PreviewVideoDTO preview : result.getPreviewVideoList()) {
                        logger.info("预览视频 - 背景色: {}, 预览地址: {}", 
                                   preview.getColorName(), preview.getResultUrl());
                    }
                }
                
                // 确认抠像效果（这里自动通过，实际使用时应该让用户确认）
                BaseResponse<VideoConfirmFlagResult> confirmResponse = client.acceptMattingPreview(taskId);
                if (confirmResponse.isSuccess()) {
                    logger.info("已确认抠像效果，继续训练");
                    
                    // 继续轮询训练结果
                    result = pollVideoTrainingResult(client, taskId);
                    if (result == null) {
                        return;
                    }
                }
            }
            
            // 4. 训练完成后配置数字人
            if (result.isCompleted()) {
                logger.info("形象训练完成，数字人ID: {}", result.getPersonaBizId());
                
                // 配置数字人信息
                String voiceBizId = "VH_XIAOBING_en-US_new_ptts_M51550_8N4vhTsj"; // 示例声音ID
                HumanEditInfoRequest editRequest = new HumanEditInfoRequest(taskId, "我的数字人", voiceBizId);
                BaseResponse<Void> editResponse = client.editHumanInfo(editRequest);
                
                if (editResponse.isSuccess()) {
                    logger.info("数字人信息配置成功");
                    
                    // 5. 数字人入驻
                    BaseResponse<Void> checkInResponse = client.checkInHuman(taskId);
                    if (checkInResponse.isSuccess()) {
                        logger.info("数字人入驻成功");
                    }
                }
            }
            
        } catch (Exception e) {
            logger.error("高级定制形象示例失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 高级定制声音示例
     */
    private static void advancedVoiceCustomizationExample(CustomizationClient client) {
        logger.info("=== 高级定制声音示例 ===");
        
        try {
            // 1. 音频噪音检测
            String audioUrl = "https://example.com/training-audio.wav";
            BaseResponse<VoiceCheckResult> checkResponse = client.checkVoice(audioUrl);
            
            if (checkResponse.isSuccess()) {
                VoiceCheckResult checkResult = checkResponse.getData();
                logger.info("音频检测结果 - 噪音级别: {}, 音量: {}, 是否合格: {}", 
                           checkResult.getNrLevel(), checkResult.getVolumn(), checkResult.isNoiseQualified());
                
                if (!checkResult.isNoiseQualified()) {
                    logger.warn("音频噪音级别过高，建议重新录制");
                    return;
                }
            }
            
            // 2. 创建声音训练任务
            String audioText = "这是用于训练声音的文本内容，请确保发音清晰，语速自然。";
            BaseResponse<Integer> submitResponse = client.submitVoiceTrainingTask(audioText, audioUrl);
            
            if (!submitResponse.isSuccess()) {
                logger.error("提交声音训练任务失败: {}", submitResponse.getMessage());
                return;
            }
            
            Integer taskId = submitResponse.getData();
            logger.info("成功提交声音训练任务，任务ID: {}", taskId);
            
            // 3. 轮询获取训练结果
            VoiceTrainingTaskResult result = pollVoiceTrainingResult(client, taskId);
            if (result == null) {
                return;
            }
            
            // 4. 训练完成后入驻声音
            if (result.isCompleted()) {
                logger.info("声音训练完成，声音bizId: {}, 声音OpenapiId: {}", 
                           result.getVoiceBizId(), result.getVoiceOpenapiId());
                
                BaseResponse<Void> checkInResponse = client.checkInVoice(taskId, "我的定制声音");
                if (checkInResponse.isSuccess()) {
                    logger.info("声音入驻成功");
                }
            }
            
        } catch (Exception e) {
            logger.error("高级定制声音示例失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 极速定制形象示例
     */
    private static void zeroShotImageCustomizationExample(CustomizationClient client) {
        logger.info("=== 极速定制形象示例 ===");
        
        try {
            // 1. 创建零样本形象定制任务
            String trainingVideoUrl = "https://example.com/zero-shot-video.mp4";
            String authVideoUrl = "https://example.com/auth-video.mp4";
            
            BaseResponse<String> commitResponse = client.commitZeroShotStudioImage(trainingVideoUrl, authVideoUrl);
            
            if (!commitResponse.isSuccess()) {
                logger.error("提交零样本形象定制任务失败: {}", commitResponse.getMessage());
                return;
            }
            
            String bizId = commitResponse.getData();
            logger.info("成功提交零样本形象定制任务，bizId: {}", bizId);
            
            // 2. 轮询质检结果
            QualityCheckInfo qualityCheck = pollQualityCheckResult(client, bizId);
            if (qualityCheck == null || !qualityCheck.isCompleted()) {
                return;
            }
            
            logger.info("质检完成，开始启动训练");
            
            // 3. 启动训练
            BaseResponse<StartTrainingRes> startResponse = client.startTraining(bizId, "极速数字人");
            
            if (!startResponse.isSuccess()) {
                logger.error("启动训练失败: {}", startResponse.getMessage());
                return;
            }
            
            StartTrainingRes startResult = startResponse.getData();
            logger.info("训练启动成功，预估耗时: {} 分钟", startResult.getEstimateMinutes());
            
            // 4. 轮询训练结果
            TrainingResult trainingResult = pollZeroShotTrainingResult(client, bizId);
            if (trainingResult == null) {
                return;
            }
            
            // 5. 训练完成后自动入驻
            if (trainingResult.isCompleted()) {
                logger.info("极速形象定制完成，数字人bizId: {}", trainingResult.getBizId());
                logger.info("数字人已自动入驻，可用于播报和交互");
            }
            
        } catch (Exception e) {
            logger.error("极速定制形象示例失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 极速定制声音示例
     */
    private static void zeroShotVoiceCustomizationExample(CustomizationClient client) {
        logger.info("=== 极速定制声音示例 ===");
        
        try {
            // 1. 创建零样本声音定制任务
            String trainingAudioUrl = "https://example.com/zero-shot-audio.mp3";
            String audioText = "这是用于生成试听音频的文本内容";
            String voiceName = "极速定制声音";
            
            BaseResponse<String> commitResponse = client.commitZeroShotVoice(trainingAudioUrl, audioText, voiceName);
            
            if (!commitResponse.isSuccess()) {
                logger.error("提交零样本声音定制任务失败: {}", commitResponse.getMessage());
                return;
            }
            
            String bizId = commitResponse.getData();
            logger.info("成功提交零样本声音定制任务，bizId: {}", bizId);
            
            // 2. 轮询训练结果
            TrainingResult result = pollZeroShotVoiceTrainingResult(client, bizId);
            if (result == null) {
                return;
            }
            
            // 3. 训练完成
            if (result.isCompleted()) {
                logger.info("极速声音定制完成");
                logger.info("声音bizId: {}", result.getVoiceBizId());
                logger.info("声音OpenapiId: {}", result.getVoiceOpenapiId());
                logger.info("试听地址: {}", result.getAuditionFile());
                logger.info("声音无需入驻，训练完成后即可用于合成播报视频、音频");
            }
            
        } catch (Exception e) {
            logger.error("极速定制声音示例失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 轮询视频训练结果
     */
    private static VideoTrainingTaskResult pollVideoTrainingResult(CustomizationClient client, String taskId) {
        logger.info("开始轮询视频训练结果...");
        
        int maxAttempts = 60; // 最多轮询60次
        int attemptCount = 0;
        
        while (attemptCount < maxAttempts) {
            try {
                BaseResponse<VideoTrainingTaskResult> response = client.getVideoTrainingTask(taskId);
                
                if (response.isSuccess()) {
                    VideoTrainingTaskResult result = response.getData();
                    logger.info("训练状态: {}, 进度: {}%", result.getStage(), result.getProgress());
                    
                    if (result.isCompleted() || result.isFailed() || result.needsPreviewConfirmation()) {
                        return result;
                    }
                }
                
                // 等待30秒后继续轮询
                TimeUnit.SECONDS.sleep(30);
                attemptCount++;
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.error("轮询被中断: {}", e.getMessage());
                break;
            } catch (Exception e) {
                logger.error("轮询训练结果失败: {}", e.getMessage());
                break;
            }
        }
        
        logger.error("轮询超时或失败");
        return null;
    }
    
    /**
     * 轮询声音训练结果
     */
    private static VoiceTrainingTaskResult pollVoiceTrainingResult(CustomizationClient client, Integer taskId) {
        logger.info("开始轮询声音训练结果...");
        
        int maxAttempts = 40; // 最多轮询40次
        int attemptCount = 0;
        
        while (attemptCount < maxAttempts) {
            try {
                BaseResponse<VoiceTrainingTaskResult> response = client.getVoiceTrainingTask(taskId);
                
                if (response.isSuccess()) {
                    VoiceTrainingTaskResult result = response.getData();
                    logger.info("训练状态: {}, 进度: {}%", result.getStage(), result.getProgress());
                    
                    if (result.isCompleted()) {
                        return result;
                    }
                }
                
                // 等待30秒后继续轮询
                TimeUnit.SECONDS.sleep(30);
                attemptCount++;
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.error("轮询被中断: {}", e.getMessage());
                break;
            } catch (Exception e) {
                logger.error("轮询训练结果失败: {}", e.getMessage());
                break;
            }
        }
        
        logger.error("轮询超时或失败");
        return null;
    }
    
    /**
     * 轮询质检结果
     */
    private static QualityCheckInfo pollQualityCheckResult(CustomizationClient client, String bizId) {
        logger.info("开始轮询质检结果...");
        
        int maxAttempts = 20; // 最多轮询20次
        int attemptCount = 0;
        
        while (attemptCount < maxAttempts) {
            try {
                BaseResponse<QualityCheckInfo> response = client.getQualityCheckResult(bizId);
                
                if (response.isSuccess()) {
                    QualityCheckInfo result = response.getData();
                    logger.info("质检状态: {}, 进度: {}%", result.getStatus(), result.getProgress());
                    
                    if (result.isCompleted() || result.isFailed()) {
                        return result;
                    }
                }
                
                // 等待15秒后继续轮询
                TimeUnit.SECONDS.sleep(15);
                attemptCount++;
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.error("轮询被中断: {}", e.getMessage());
                break;
            } catch (Exception e) {
                logger.error("轮询质检结果失败: {}", e.getMessage());
                break;
            }
        }
        
        logger.error("质检轮询超时或失败");
        return null;
    }
    
    /**
     * 轮询零样本训练结果
     */
    private static TrainingResult pollZeroShotTrainingResult(CustomizationClient client, String bizId) {
        logger.info("开始轮询零样本训练结果...");
        
        int maxAttempts = 30; // 最多轮询30次
        int attemptCount = 0;
        
        while (attemptCount < maxAttempts) {
            try {
                BaseResponse<TrainingResult> response = client.getZeroShotTrainingResult(bizId);
                
                if (response.isSuccess()) {
                    TrainingResult result = response.getData();
                    logger.info("训练状态: {}, 进度: {}%", result.getStatus(), result.getProgress());
                    
                    if (result.isCompleted() || result.isFailed()) {
                        return result;
                    }
                }
                
                // 等待30秒后继续轮询
                TimeUnit.SECONDS.sleep(30);
                attemptCount++;
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.error("轮询被中断: {}", e.getMessage());
                break;
            } catch (Exception e) {
                logger.error("轮询训练结果失败: {}", e.getMessage());
                break;
            }
        }
        
        logger.error("轮询超时或失败");
        return null;
    }
    
    /**
     * 轮询零样本声音训练结果
     */
    private static TrainingResult pollZeroShotVoiceTrainingResult(CustomizationClient client, String bizId) {
        logger.info("开始轮询零样本声音训练结果...");
        
        int maxAttempts = 10; // 最多轮询10次，声音训练很快
        int attemptCount = 0;
        
        while (attemptCount < maxAttempts) {
            try {
                BaseResponse<TrainingResult> response = client.getZeroShotVoiceTrainingResult(bizId);
                
                if (response.isSuccess()) {
                    TrainingResult result = response.getData();
                    logger.info("训练状态: {}", result.getStatus());
                    
                    if (result.isCompleted() || result.isFailed()) {
                        return result;
                    }
                }
                
                // 等待10秒后继续轮询
                TimeUnit.SECONDS.sleep(10);
                attemptCount++;
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.error("轮询被中断: {}", e.getMessage());
                break;
            } catch (Exception e) {
                logger.error("轮询训练结果失败: {}", e.getMessage());
                break;
            }
        }
        
        logger.error("轮询超时或失败");
        return null;
    }
}