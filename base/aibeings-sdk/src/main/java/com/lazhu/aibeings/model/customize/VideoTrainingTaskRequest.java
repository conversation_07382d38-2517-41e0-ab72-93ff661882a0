package com.lazhu.aibeings.model.customize;

import com.lazhu.aibeings.model.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 创建形象训练任务请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class VideoTrainingTaskRequest extends BaseRequest {
    
    /**
     * 拍摄的训练视频地址
     */
    private String videoUrl;
    
    /**
     * 是否对视频做抠图处理，默认0
     * 0: 保留拍摄背景 不抠图
     * 3: 仅抠人像(适合无道具) 拍摄时请确保发色及服饰，与背景保持明显色差
     * 5: 仅抠绿幕(可保留道具，如桌椅) 拍摄时请将绿幕铺满背景，避免绿幕内有阴影，不要穿戴黄色、绿色服饰
     */
    private Integer greenMattingType;
    
    /**
     * 数字人工作场景，默认1
     * 1: 播报 2: 交互
     */
    private Integer trainVersionFlag;
    
    // 便捷构造函数
    public VideoTrainingTaskRequest(String videoUrl) {
        this.videoUrl = videoUrl;
    }
    
    public VideoTrainingTaskRequest(String videoUrl, Integer greenMattingType, Integer trainVersionFlag) {
        this.videoUrl = videoUrl;
        this.greenMattingType = greenMattingType;
        this.trainVersionFlag = trainVersionFlag;
    }
    
    /**
     * 抠图类型枚举
     */
    public enum GreenMattingType {
        NO_MATTING(0, "保留拍摄背景 不抠图"),
        PORTRAIT_ONLY(3, "仅抠人像(适合无道具)"),
        GREEN_SCREEN(5, "仅抠绿幕(可保留道具，如桌椅)");
        
        private final int code;
        private final String description;
        
        GreenMattingType(int code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public int getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 训练版本标志枚举
     */
    public enum TrainVersionFlag {
        STUDIO(1, "播报"),
        INTERACTIVE(2, "交互");
        
        private final int code;
        private final String description;
        
        TrainVersionFlag(int code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public int getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    @Override
    public String toString() {
        return "VideoTrainingTaskRequest{" +
                "videoUrl='" + videoUrl + '\'' +
                ", greenMattingType=" + greenMattingType +
                ", trainVersionFlag=" + trainVersionFlag +
                '}';
    }
}