package com.lazhu.aibeings;

import com.lazhu.aibeings.client.DataManagementClient;
import com.lazhu.aibeings.client.AccountManagementClient;
import com.lazhu.aibeings.client.CustomizationClient;
import com.lazhu.aibeings.client.BroadcastingClient;
import com.lazhu.aibeings.config.AIBeingsConfig;
import com.lazhu.aibeings.exception.AIBeingsException;
import cn.hutool.core.util.StrUtil;

/**
 * Main AI Beings SDK Client
 * 
 * <AUTHOR> SDK Generator
 * @version 1.0.0
 */
public class AIBeingsClient {
    
    private final AIBeingsConfig config;
    private final DataManagementClient dataManagementClient;
    private final AccountManagementClient accountManagementClient;
    private final CustomizationClient customizationClient;
    private final BroadcastingClient broadcastingClient;
    
    /**
     * Create AI Beings client with subscription key
     * 
     * @param subscriptionKey Subscription key
     */
    public AIBeingsClient(String subscriptionKey) {
        this(new AIBeingsConfig(subscriptionKey));
    }
    
    /**
     * Create AI Beings client with custom base URL
     * 
     * @param baseUrl Base URL
     * @param subscriptionKey Subscription key
     */
    public AIBeingsClient(String baseUrl, String subscriptionKey) {
        this(new AIBeingsConfig(baseUrl, subscriptionKey));
    }
    
    /**
     * Create AI Beings client with configuration
     * 
     * @param config Configuration
     */
    public AIBeingsClient(AIBeingsConfig config) {
        validateConfig(config);
        
        this.config = config;
        this.dataManagementClient = new DataManagementClient(config);
        this.accountManagementClient = new AccountManagementClient(config);
        this.customizationClient = new CustomizationClient(config);
        this.broadcastingClient = new BroadcastingClient(new com.lazhu.aibeings.client.HttpClient(config));
    }
    
    private void validateConfig(AIBeingsConfig config) {
        if (config == null) {
            throw new AIBeingsException("Configuration cannot be null");
        }
        
        if (StrUtil.isBlank(config.getSubscriptionKey())) {
            throw new AIBeingsException("Subscription key cannot be empty");
        }
        
        if (StrUtil.isBlank(config.getBaseUrl())) {
            throw new AIBeingsException("Base URL cannot be empty");
        }
    }
    
    /**
     * Get data management client
     * 
     * @return Data management client instance
     */
    public DataManagementClient dataManagement() {
        return dataManagementClient;
    }
    
    /**
     * Get account management client
     * 
     * @return Account management client instance
     */
    public AccountManagementClient accountManagement() {
        return accountManagementClient;
    }
    
    /**
     * Get customization client
     * 
     * @return Customization client instance
     */
    public CustomizationClient customization() {
        return customizationClient;
    }
    
    /**
     * Get broadcasting client
     * 
     * @return Broadcasting client instance
     */
    public BroadcastingClient broadcasting() {
        return broadcastingClient;
    }
    
    /**
     * Get configuration
     * 
     * @return Configuration instance
     */
    public AIBeingsConfig getConfig() {
        return config;
    }
    
    /**
     * Create a builder for fluent configuration
     * 
     * @return Builder instance
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * Builder class for fluent configuration
     */
    public static class Builder {
        private String baseUrl = "https://aibeings-vip.xiaoice.com";
        private String subscriptionKey;
        private int connectTimeout = 30000;
        private int readTimeout = 60000;
        private int maxRetries = 3;
        
        public Builder baseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
            return this;
        }
        
        public Builder subscriptionKey(String subscriptionKey) {
            this.subscriptionKey = subscriptionKey;
            return this;
        }
        
        public Builder connectTimeout(int connectTimeout) {
            this.connectTimeout = connectTimeout;
            return this;
        }
        
        public Builder readTimeout(int readTimeout) {
            this.readTimeout = readTimeout;
            return this;
        }
        
        public Builder maxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
            return this;
        }
        
        public AIBeingsClient build() {
            AIBeingsConfig config = new AIBeingsConfig(baseUrl, subscriptionKey);
            config.setConnectTimeout(connectTimeout);
            config.setReadTimeout(readTimeout);
            config.setMaxRetries(maxRetries);
            
            return new AIBeingsClient(config);
        }
    }
}