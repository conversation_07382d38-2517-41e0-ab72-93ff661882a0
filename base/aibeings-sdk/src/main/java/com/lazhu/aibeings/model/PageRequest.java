package com.lazhu.aibeings.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 分页请求模型
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class PageRequest extends BaseRequest {
    
    @JsonProperty("pageIndex")
    private Integer pageIndex;
    
    @JsonProperty("pageSize")
    private Integer pageSize;
    
    // 便捷构造函数
    public PageRequest(Integer pageIndex, Integer pageSize) {
        super();
        this.pageIndex = pageIndex;
        this.pageSize = pageSize;
    }
}