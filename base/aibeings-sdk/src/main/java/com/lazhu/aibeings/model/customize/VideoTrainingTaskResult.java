package com.lazhu.aibeings.model.customize;

import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 形象训练任务结果
 */
@Data
@NoArgsConstructor
public class VideoTrainingTaskResult {
    
    /**
     * 训练阶段
     */
    private String stage;
    
    /**
     * 进度 1-100
     */
    private Integer progress;
    
    /**
     * 失败原因
     */
    private String message;
    
    /**
     * 数字人ID
     */
    private String personaBizId;
    
    /**
     * 加密后的数字人ID
     */
    private String virtualHumanId;
    
    /**
     * 数字人动作ID
     */
    private String postureInfoBizId;
    
    /**
     * 抠像预览结果，当stage为JUST_CHECK_DONE时候可获取当前字段值
     */
    private List<PreviewVideoDTO> previewVideoList;
    
    /**
     * 创建时间
     */
    private String createTime;
    

    
    /**
     * 训练阶段枚举
     */
    public enum Stage {
        INIT("INIT", "任务初始化"),
        WAITING("WAITING", "排队中"),
        CHECK("CHECK", "质检中"),
        JUST_CHECK("JUST_CHECK", "质检中"),
        JUST_CHECK_DONE("JUST_CHECK_DONE", "质检完成"),
        CHECK_FAILED("CHECK_FAILED", "质检失败"),
        USER_REJECT_PREVIEW("USER_REJECT_PREVIEW", "用户拒绝预览结果"),
        USER_CONFIRM_TIMEOUT("USER_CONFIRM_TIMEOUT", "抠像效果确认超时"),
        SUCCEED("SUCCEED", "训练完成"),
        TRAINING("TRAINING", "训练中"),
        FAILED("FAILED", "训练失败"),
        FINISH("FINISH", "已入驻");
        
        private final String code;
        private final String description;
        
        Stage(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 是否训练完成
     */
    public boolean isCompleted() {
        return Stage.SUCCEED.getCode().equals(stage) || Stage.FINISH.getCode().equals(stage);
    }
    
    /**
     * 是否训练失败
     */
    public boolean isFailed() {
        return Stage.FAILED.getCode().equals(stage) || Stage.CHECK_FAILED.getCode().equals(stage);
    }
    
    /**
     * 是否需要确认抠像效果
     */
    public boolean needsPreviewConfirmation() {
        return Stage.JUST_CHECK_DONE.getCode().equals(stage);
    }
    
    /**
     * 是否正在进行中
     */
    public boolean isInProgress() {
        return Stage.WAITING.getCode().equals(stage) || 
               Stage.TRAINING.getCode().equals(stage) ||
               Stage.CHECK.getCode().equals(stage) ||
               Stage.JUST_CHECK.getCode().equals(stage);
    }
    
    @Override
    public String toString() {
        return "VideoTrainingTaskResult{" +
                "stage='" + stage + '\'' +
                ", progress=" + progress +
                ", message='" + message + '\'' +
                ", personaBizId='" + personaBizId + '\'' +
                ", virtualHumanId='" + virtualHumanId + '\'' +
                ", postureInfoBizId='" + postureInfoBizId + '\'' +
                ", previewVideoList=" + previewVideoList +
                ", createTime='" + createTime + '\'' +
                '}';
    }
}