package com.lazhu.aibeings.model.customize;

import com.lazhu.aibeings.model.BaseRequest;

/**
 * 数字人入驻/取消入驻请求
 */
public class CheckInHumanRequest extends BaseRequest {
    
    /**
     * 定制任务的bizId
     */
    private String bizId;
    
    /**
     * true: 入驻 false: 取消入驻
     */
    private Boolean checkIn;
    
    public CheckInHumanRequest() {
        super();
    }
    
    public CheckInHumanRequest(String bizId, Boolean checkIn) {
        super();
        this.bizId = bizId;
        this.checkIn = checkIn;
    }
    
    public String getBizId() {
        return bizId;
    }
    
    public void setBizId(String bizId) {
        this.bizId = bizId;
    }
    
    public Boolean getCheckIn() {
        return checkIn;
    }
    
    public void setCheckIn(Boolean checkIn) {
        this.checkIn = checkIn;
    }
}