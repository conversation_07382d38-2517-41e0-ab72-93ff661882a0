package com.lazhu.aibeings.model.customize;

import com.lazhu.aibeings.model.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 创建零样本形象定制任务请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class ZeroShotImageRequest extends BaseRequest {
    
    /**
     * 数字人工作场景
     * 播报：STUDIO 交互： INTERACTIVE
     */
    private String vhModelType;
    
    /**
     * 训练视频URL
     */
    private String trainingVideoUrl;
    
    /**
     * 授权视频地址
     * 授权视频与授权文件至少上传一个
     */
    private String authVideoUrl;
    
    /**
     * 授权文件地址
     * 授权视频与授权文件至少上传一个
     */
    private String authFileUrl;
    
    /**
     * 播报训练视频为10s-30min全程讲话视频时，需传入1；
     * 播报训练视频为35-45s且前30s不讲话时，不传
     */
    private Integer voiceTemplateVersion;
    
    // 便捷构造函数
    public ZeroShotImageRequest(String vhModelType, String trainingVideoUrl) {
        this.vhModelType = vhModelType;
        this.trainingVideoUrl = trainingVideoUrl;
    }
    
    /**
     * 数字人工作场景枚举
     */
    public enum ModelType {
        STUDIO("STUDIO", "播报"),
        INTERACTIVE("INTERACTIVE", "交互");
        
        private final String code;
        private final String description;
        
        ModelType(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    @Override
    public String toString() {
        return "ZeroShotImageRequest{" +
                "vhModelType='" + vhModelType + '\'' +
                ", trainingVideoUrl='" + trainingVideoUrl + '\'' +
                ", authVideoUrl='" + authVideoUrl + '\'' +
                ", authFileUrl='" + authFileUrl + '\'' +
                ", voiceTemplateVersion=" + voiceTemplateVersion +
                '}';
    }
}