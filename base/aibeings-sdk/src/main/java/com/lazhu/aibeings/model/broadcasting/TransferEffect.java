package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 转场动画
 */
@Data
@NoArgsConstructor
public class TransferEffect {
    
    /**
     * 转场动画类型
     */
    @JsonProperty("type")
    private String type;
    
    /**
     * 转场动画持续时长(秒)
     */
    @JsonProperty("duration")
    private Float duration;
    
    // 转场动画类型常量
    public static final String WIPE_LEFT = "wipe_left";
    public static final String WIPE_RIGHT = "wipe_right";
    public static final String WIPE_UP = "wipe_up";
    public static final String WIPE_DOWN = "wipe_down";
    public static final String FADE_IN = "fade_in";
    public static final String FADE_OUT = "fade_out";
    public static final String DISSOLVE = "dissolve";
    public static final String SLIDE_LEFT = "slide_left";
    public static final String SLIDE_RIGHT = "slide_right";
    public static final String SLIDE_UP = "slide_up";
    public static final String SLIDE_DOWN = "slide_down";
    
    // 便捷构造函数
    public TransferEffect(String type, Float duration) {
        this.type = type;
        this.duration = duration;
    }
    
    /**
     * 创建左擦除转场
     */
    public static TransferEffect wipeLeft(float duration) {
        return new TransferEffect(WIPE_LEFT, duration);
    }
    
    /**
     * 创建右擦除转场
     */
    public static TransferEffect wipeRight(float duration) {
        return new TransferEffect(WIPE_RIGHT, duration);
    }
    
    /**
     * 创建上擦除转场
     */
    public static TransferEffect wipeUp(float duration) {
        return new TransferEffect(WIPE_UP, duration);
    }
    
    /**
     * 创建下擦除转场
     */
    public static TransferEffect wipeDown(float duration) {
        return new TransferEffect(WIPE_DOWN, duration);
    }
    
    /**
     * 创建淡入转场
     */
    public static TransferEffect fadeIn(float duration) {
        return new TransferEffect(FADE_IN, duration);
    }
    
    /**
     * 创建淡出转场
     */
    public static TransferEffect fadeOut(float duration) {
        return new TransferEffect(FADE_OUT, duration);
    }
    
    /**
     * 创建溶解转场
     */
    public static TransferEffect dissolve(float duration) {
        return new TransferEffect(DISSOLVE, duration);
    }
    
    /**
     * 创建滑动转场
     */
    public static TransferEffect slide(String direction, float duration) {
        String effectType;
        switch (direction.toLowerCase()) {
            case "left":
                effectType = SLIDE_LEFT;
                break;
            case "right":
                effectType = SLIDE_RIGHT;
                break;
            case "up":
                effectType = SLIDE_UP;
                break;
            case "down":
                effectType = SLIDE_DOWN;
                break;
            default:
                effectType = SLIDE_LEFT;
        }
        return new TransferEffect(effectType, duration);
    }
    
    /**
     * 验证转场效果
     */
    public boolean isValid() {
        return type != null && !type.trim().isEmpty() && duration != null && duration > 0;
    }
}