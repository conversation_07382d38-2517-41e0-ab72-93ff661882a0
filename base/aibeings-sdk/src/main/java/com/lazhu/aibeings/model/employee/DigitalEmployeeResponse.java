package com.lazhu.aibeings.model.employee;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 数字员工查询响应数据模型
 */
@Data
@NoArgsConstructor
public class DigitalEmployeeResponse {
    
    @JsonProperty("records")
    private List<DigitalEmployee> records;
    
    @JsonProperty("total")
    private Long total;
    
    @JsonProperty("size")
    private Integer size;
    
    @JsonProperty("current")
    private Integer current;
    
    @JsonProperty("pages")
    private Integer pages;
    
    @JsonProperty("orders")
    private List<Object> orders;
    
    @JsonProperty("hitCount")
    private Boolean hitCount;
    
    @JsonProperty("searchCount")
    private Boolean searchCount;
    

    
    /**
     * 是否还有下一页
     */
    public boolean hasNext() {
        return current != null && pages != null && current < pages;
    }
    
    /**
     * 是否有上一页
     */
    public boolean hasPrevious() {
        return current != null && current > 1;
    }
}