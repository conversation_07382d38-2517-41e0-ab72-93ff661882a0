package com.lazhu.aibeings.model.customize;

import com.lazhu.aibeings.model.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 训练音频噪音检测请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class VoiceCheckRequest extends BaseRequest {
    
    /**
     * 音频文件url
     */
    private String audio;
    
    // 便捷构造函数
    public VoiceCheckRequest(String audio) {
        this.audio = audio;
    }
}