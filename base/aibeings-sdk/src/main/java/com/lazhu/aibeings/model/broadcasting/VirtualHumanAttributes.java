package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 虚拟人属性
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VirtualHumanAttributes {
    
    /**
     * 虚拟人的宽度
     */
    @JsonProperty("width")
    private Integer width;
    
    /**
     * 虚拟人的高度
     */
    @JsonProperty("height")
    private Integer height;
    
    /**
     * 虚拟人在视频画布中的水平位置，即x轴坐标值
     */
    @JsonProperty("x")
    private Integer x;
    
    /**
     * 虚拟人在视频画布中的垂直位置，即y轴坐标值
     */
    @JsonProperty("y")
    private Integer y;
    

    
    /**
     * 创建居中位置的虚拟人属性
     * 
     * @param videoWidth 视频宽度
     * @param videoHeight 视频高度
     * @param humanWidth 虚拟人宽度
     * @param humanHeight 虚拟人高度
     * @return 居中的虚拟人属性
     */
    public static VirtualHumanAttributes centered(int videoWidth, int videoHeight, 
                                                 int humanWidth, int humanHeight) {
        int x = (videoWidth - humanWidth) / 2;
        int y = (videoHeight - humanHeight) / 2;
        return new VirtualHumanAttributes(humanWidth, humanHeight, x, y);
    }
    
    /**
     * 创建底部居中位置的虚拟人属性
     */
    public static VirtualHumanAttributes bottomCentered(int videoWidth, int videoHeight, 
                                                       int humanWidth, int humanHeight) {
        int x = (videoWidth - humanWidth) / 2;
        int y = videoHeight - humanHeight;
        return new VirtualHumanAttributes(humanWidth, humanHeight, x, y);
    }
    
    /**
     * 验证属性
     */
    public boolean isValid() {
        return width != null && width > 0
                && height != null && height > 0
                && x != null && x >= 0
                && y != null && y >= 0;
    }
    
    /**
     * 计算宽高比
     */
    public double getAspectRatio() {
        if (height == null || height == 0) {
            return 0;
        }
        return (double) width / height;
    }
}