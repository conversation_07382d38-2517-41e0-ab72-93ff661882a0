package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 显示视频属性
 */
@Data
@NoArgsConstructor
public class DisplayVideoAttributes {
    
    /**
     * 视频宽度
     */
    @JsonProperty("width")
    private Integer width;
    
    /**
     * 视频高度
     */
    @JsonProperty("height")
    private Integer height;
    
    /**
     * 视频坐标x值
     */
    @JsonProperty("x")
    private Integer x;
    
    /**
     * 视频坐标y值
     */
    @JsonProperty("y")
    private Integer y;
    
    /**
     * 音量
     */
    @JsonProperty("volume")
    private Double volume = 1.0;
    
    /**
     * 是否循环播放
     */
    @JsonProperty("loop")
    private Boolean loop = false;
    
    /**
     * 视频的长度（毫秒）
     */
    @JsonProperty("durationMilliseconds")
    private Integer durationMilliseconds;
    
    // 便捷构造函数
    public DisplayVideoAttributes(Integer width, Integer height, Integer x, Integer y, Integer durationMilliseconds) {
        this.width = width;
        this.height = height;
        this.x = x;
        this.y = y;
        this.durationMilliseconds = durationMilliseconds;
    }
    
    /**
     * 设置音量
     */
    public DisplayVideoAttributes withVolume(double volume) {
        if (volume >= 0.0 && volume <= 1.0) {
            this.volume = volume;
        }
        return this;
    }
    
    /**
     * 设置循环播放
     */
    public DisplayVideoAttributes withLoop(boolean loop) {
        this.loop = loop;
        return this;
    }
    
    /**
     * 创建居中位置的视频属性
     */
    public static DisplayVideoAttributes centered(int videoWidth, int videoHeight, 
                                                 int displayWidth, int displayHeight, 
                                                 int durationMs) {
        int x = (videoWidth - displayWidth) / 2;
        int y = (videoHeight - displayHeight) / 2;
        return new DisplayVideoAttributes(displayWidth, displayHeight, x, y, durationMs);
    }
    
    /**
     * 验证视频属性
     */
    public boolean isValid() {
        if (width == null || height == null || x == null || y == null || durationMilliseconds == null) {
            return false;
        }
        if (width <= 10 || height <= 10 || x < 0 || y < 0 || durationMilliseconds <= 0) {
            return false;
        }
        if (volume != null && (volume < 0.0 || volume > 1.0)) {
            return false;
        }
        return true;
    }
}