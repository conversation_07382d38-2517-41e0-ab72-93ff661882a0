package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 显示图片配置
 */
public class DisplayImage {
    
    /**
     * 图片URL
     */
    @JsonProperty("mediaUrl")
    private String mediaUrl;
    
    /**
     * 图层顺序
     */
    @JsonProperty("zIndex")
    private Integer zIndex;
    
    /**
     * 图片属性
     */
    @JsonProperty("attributes")
    private DisplayImageAttributes attributes;
    
    // Constructors
    public DisplayImage() {}
    
    public DisplayImage(String mediaUrl, Integer zIndex, DisplayImageAttributes attributes) {
        this.mediaUrl = mediaUrl;
        this.zIndex = zIndex;
        this.attributes = attributes;
    }
    
    // Getters and Setters
    public String getMediaUrl() {
        return mediaUrl;
    }
    
    public void setMediaUrl(String mediaUrl) {
        this.mediaUrl = mediaUrl;
    }
    
    public Integer getZIndex() {
        return zIndex;
    }
    
    public void setZIndex(Integer zIndex) {
        this.zIndex = zIndex;
    }
    
    public DisplayImageAttributes getAttributes() {
        return attributes;
    }
    
    public void setAttributes(DisplayImageAttributes attributes) {
        this.attributes = attributes;
    }
    
    /**
     * 验证显示图片配置
     */
    public boolean isValid() {
        return mediaUrl != null && !mediaUrl.trim().isEmpty()
                && zIndex != null && zIndex >= 1 && zIndex <= 100
                && attributes != null && attributes.isValid();
    }
}