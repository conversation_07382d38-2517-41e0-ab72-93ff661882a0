package com.lazhu.aibeings.client;

import com.lazhu.aibeings.config.AIBeingsConfig;
import com.lazhu.aibeings.model.BaseResponse;
import com.lazhu.aibeings.model.customize.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

/**
 * 定制数字人客户端
 * 
 * 提供数字人定制功能，包括：
 * - 高级定制形象：上传3～6分钟视频，定制高级播报、交互形象
 * - 高级定制声音：上传5～12分钟音频，定制高级声音
 * - 极速定制形象：分钟级生成播报分身，小时级尊享交互体验
 * - 极速定制声音：秒级复刻声音
 */
public class CustomizationClient {
    
    private static final Logger logger = LoggerFactory.getLogger(CustomizationClient.class);
    
    private final HttpClient httpClient;
    
    public CustomizationClient(AIBeingsConfig config) {
        // 使用正确的基础URL
        AIBeingsConfig customizeConfig = new AIBeingsConfig(config.getSubscriptionKey());
        customizeConfig.setBaseUrl("https://openapi.xiaoice.com/vh");
        customizeConfig.setConnectTimeout(config.getConnectTimeout());
        customizeConfig.setReadTimeout(config.getReadTimeout());
        customizeConfig.setMaxRetries(config.getMaxRetries());
        
        this.httpClient = new HttpClient(customizeConfig);
    }
    
    // ========== 高级定制形象 APIs ==========
    
    /**
     * 创建形象训练任务
     * 
     * @param request 视频训练任务请求
     * @return 任务ID
     */
    public BaseResponse<String> submitVideoTrainingTask(VideoTrainingTaskRequest request) {
        logger.debug("Submitting video training task with videoUrl: {}, greenMattingType: {}, trainVersionFlag: {}", 
                    request.getVideoUrl(), request.getGreenMattingType(), request.getTrainVersionFlag());
        
        return httpClient.post("/openapi/customize/submitVideoTrainingTask", request, String.class);
    }
    
    /**
     * 创建播报形象训练任务
     * 
     * @param videoUrl 训练视频地址
     * @return 任务ID
     */
    public BaseResponse<String> submitStudioVideoTraining(String videoUrl) {
        VideoTrainingTaskRequest request = new VideoTrainingTaskRequest(videoUrl);
        request.setTrainVersionFlag(VideoTrainingTaskRequest.TrainVersionFlag.STUDIO.getCode());
        return submitVideoTrainingTask(request);
    }
    
    /**
     * 创建交互形象训练任务
     * 
     * @param videoUrl 训练视频地址
     * @return 任务ID
     */
    public BaseResponse<String> submitInteractiveVideoTraining(String videoUrl) {
        VideoTrainingTaskRequest request = new VideoTrainingTaskRequest(videoUrl);
        request.setTrainVersionFlag(VideoTrainingTaskRequest.TrainVersionFlag.INTERACTIVE.getCode());
        return submitVideoTrainingTask(request);
    }
    
    /**
     * 获取形象训练结果
     * 
     * @param taskId 任务ID
     * @return 训练结果
     */
    public BaseResponse<VideoTrainingTaskResult> getVideoTrainingTask(String taskId) {
        logger.debug("Getting video training task result for taskId: {}", taskId);
        
        return httpClient.get("/openapi/customize/getVideoTrainingTask", 
                            java.util.Collections.singletonMap("taskId", taskId), 
                            VideoTrainingTaskResult.class);
    }
    
    /**
     * 确认抠像效果
     * 
     * @param request 抠像确认请求
     * @return 确认结果
     */
    public BaseResponse<VideoConfirmFlagResult> confirmMatting(MattingConfirmRequest request) {
        logger.debug("Confirming matting effect for taskId: {}, confirmFlag: {}", 
                    request.getTaskId(), request.getConfirmFlag());
        
        return httpClient.post("/openapi/customize/mattingConfirm", request, VideoConfirmFlagResult.class);
    }
    
    /**
     * 通过抠像效果，继续训练
     * 
     * @param taskId 任务ID
     * @return 确认结果
     */
    public BaseResponse<VideoConfirmFlagResult> acceptMattingPreview(String taskId) {
        return confirmMatting(new MattingConfirmRequest(taskId, true));
    }
    
    /**
     * 拒绝抠像效果，任务失败
     * 
     * @param taskId 任务ID
     * @return 确认结果
     */
    public BaseResponse<VideoConfirmFlagResult> rejectMattingPreview(String taskId) {
        return confirmMatting(new MattingConfirmRequest(taskId, false));
    }
    
    /**
     * 入驻前配置数字人
     * 
     * @param request 配置请求
     * @return 配置结果
     */
    public BaseResponse<Void> editHumanInfo(HumanEditInfoRequest request) {
        logger.debug("Editing human info for taskId: {}, name: {}, bizId: {}", 
                    request.getTaskId(), request.getName(), request.getBizId());
        
        return httpClient.post("/openapi/customize/human/editInfo", request, Void.class);
    }
    
    /**
     * 数字人入驻/取消入驻
     * 
     * @param request 入驻请求
     * @return 入驻结果
     */
    public BaseResponse<Void> checkInHuman(HumanCheckInRequest request) {
        logger.debug("Checking in human for taskId: {}, checkIn: {}", 
                    request.getTaskId(), request.getCheckIn());
        
        return httpClient.post("/openapi/customize/human/checkIn", request, Void.class);
    }
    
    /**
     * 数字人入驻
     * 
     * @param taskId 任务ID
     * @return 入驻结果
     */
    public BaseResponse<Void> checkInHuman(String taskId) {
        return checkInHuman(new HumanCheckInRequest(taskId, true));
    }
    
    /**
     * 数字人取消入驻
     * 
     * @param taskId 任务ID
     * @return 取消入驻结果
     */
    public BaseResponse<Void> checkOutHuman(String taskId) {
        return checkInHuman(new HumanCheckInRequest(taskId, false));
    }
    
    // ========== 高级定制声音 APIs ==========
    
    /**
     * 创建声音训练任务
     * 
     * @param request 声音训练任务请求
     * @return 任务ID
     */
    public BaseResponse<Integer> submitVoiceTrainingTask(VoiceTrainingTaskRequest request) {
        logger.debug("Submitting voice training task with {} audio files", 
                    request.getTrainingData() != null ? request.getTrainingData().size() : 0);
        
        return httpClient.post("/openapi/customize/submitVoiceTrainingTask", request, Integer.class);
    }
    
    /**
     * 创建声音训练任务 - 简化版本
     * 
     * @param audioText 音频文本
     * @param audioUrl 音频地址
     * @return 任务ID
     */
    public BaseResponse<Integer> submitVoiceTrainingTask(String audioText, String audioUrl) {
        AudioTrainingData data = new AudioTrainingData(audioText, audioUrl);
        VoiceTrainingTaskRequest request = new VoiceTrainingTaskRequest(Arrays.asList(data));
        return submitVoiceTrainingTask(request);
    }
    
    /**
     * 获取声音训练结果
     * 
     * @param taskId 任务ID
     * @return 训练结果
     */
    public BaseResponse<VoiceTrainingTaskResult> getVoiceTrainingTask(Integer taskId) {
        logger.debug("Getting voice training task result for taskId: {}", taskId);
        
        return httpClient.get("/openapi/customize/get-voice-training-task/v2", 
                            java.util.Collections.singletonMap("taskId", taskId), 
                            VoiceTrainingTaskResult.class);
    }
    
    /**
     * 训练音频噪音检测
     * 
     * @param request 检测请求
     * @return 检测结果
     */
    public BaseResponse<VoiceCheckResult> checkVoice(VoiceCheckRequest request) {
        logger.debug("Checking voice quality for audio: {}", request.getAudio());
        
        return httpClient.post("/openapi/customize/voice-check", request, VoiceCheckResult.class);
    }
    
    /**
     * 训练音频噪音检测 - 简化版本
     * 
     * @param audioUrl 音频地址
     * @return 检测结果
     */
    public BaseResponse<VoiceCheckResult> checkVoice(String audioUrl) {
        return checkVoice(new VoiceCheckRequest(audioUrl));
    }
    
    /**
     * 声音入驻/取消入驻
     * 
     * @param request 入驻请求
     * @return 入驻结果
     */
    public BaseResponse<Void> checkInVoice(VoiceCheckInRequest request) {
        logger.debug("Checking in voice for taskId: {}, checkIn: {}, voiceName: {}", 
                    request.getTaskId(), request.getCheckIn(), request.getVoiceName());
        
        return httpClient.post("/openapi/customize/voice/checkIn", request, Void.class);
    }
    
    /**
     * 声音入驻
     * 
     * @param taskId 任务ID
     * @param voiceName 声音名称
     * @return 入驻结果
     */
    public BaseResponse<Void> checkInVoice(Integer taskId, String voiceName) {
        return checkInVoice(new VoiceCheckInRequest(taskId, true, voiceName));
    }
    
    /**
     * 声音取消入驻
     * 
     * @param taskId 任务ID
     * @return 取消入驻结果
     */
    public BaseResponse<Void> checkOutVoice(Integer taskId) {
        return checkInVoice(new VoiceCheckInRequest(taskId, false));
    }
    
    // ========== 极速定制形象 APIs ==========
    
    /**
     * 创建零样本形象定制任务
     * 
     * @param request 零样本形象请求
     * @return 任务bizId
     */
    public BaseResponse<String> commitZeroShotImage(ZeroShotImageRequest request) {
        logger.debug("Committing zero-shot image customization for modelType: {}, videoUrl: {}", 
                    request.getVhModelType(), request.getTrainingVideoUrl());
        
        return httpClient.post("/openapi/customize/zero/commit", request, String.class);
    }
    
    /**
     * 创建零样本播报形象定制任务
     * 
     * @param trainingVideoUrl 训练视频地址
     * @param authVideoUrl 授权视频地址
     * @return 任务bizId
     */
    public BaseResponse<String> commitZeroShotStudioImage(String trainingVideoUrl, String authVideoUrl) {
        ZeroShotImageRequest request = new ZeroShotImageRequest(
            ZeroShotImageRequest.ModelType.STUDIO.getCode(), trainingVideoUrl);
        request.setAuthVideoUrl(authVideoUrl);
        return commitZeroShotImage(request);
    }
    
    /**
     * 创建零样本交互形象定制任务
     * 
     * @param trainingVideoUrl 训练视频地址
     * @param authVideoUrl 授权视频地址
     * @return 任务bizId
     */
    public BaseResponse<String> commitZeroShotInteractiveImage(String trainingVideoUrl, String authVideoUrl) {
        ZeroShotImageRequest request = new ZeroShotImageRequest(
            ZeroShotImageRequest.ModelType.INTERACTIVE.getCode(), trainingVideoUrl);
        request.setAuthVideoUrl(authVideoUrl);
        return commitZeroShotImage(request);
    }
    
    /**
     * 查询质检结果（轮询）
     * 
     * @param bizId 定制任务的bizId
     * @return 质检结果
     */
    public BaseResponse<QualityCheckInfo> getQualityCheckResult(String bizId) {
        logger.debug("Getting quality check result for bizId: {}", bizId);
        
        return httpClient.get("/openapi/customize/zero/get-ckq-result", 
                            java.util.Collections.singletonMap("bizId", bizId), 
                            QualityCheckInfo.class);
    }
    
    /**
     * 启动训练
     * 
     * @param request 启动训练请求
     * @return 启动结果
     */
    public BaseResponse<StartTrainingRes> startTraining(StartTrainingRequest request) {
        logger.debug("Starting training for bizId: {}, name: {}, voiceBizId: {}", 
                    request.getBizId(), request.getName(), request.getVoiceBizId());
        
        return httpClient.post("/openapi/customize/zero/start-training", request, StartTrainingRes.class);
    }
    
    /**
     * 启动训练 - 简化版本
     * 
     * @param bizId 任务bizId
     * @param name 数字人名字
     * @return 启动结果
     */
    public BaseResponse<StartTrainingRes> startTraining(String bizId, String name) {
        return startTraining(new StartTrainingRequest(bizId, name));
    }
    
    /**
     * 查询训练结果（轮询）
     * 
     * @param bizId 定制任务的bizId
     * @return 训练结果
     */
    public BaseResponse<TrainingResult> getZeroShotTrainingResult(String bizId) {
        logger.debug("Getting zero-shot training result for bizId: {}", bizId);
        
        GetTrainingResultRequest request = new GetTrainingResultRequest(bizId);
        
        return httpClient.post("/openapi/customize/zero/get-training-result", 
                             request, 
                             TrainingResult.class);
    }
    
    /**
     * 数字人入驻/取消入驻（零样本）
     * 
     * @param bizId 定制任务的bizId
     * @param checkIn true: 入驻 false: 取消入驻
     * @return 入驻结果
     */
    public BaseResponse<Boolean> checkInZeroShotHuman(String bizId, Boolean checkIn) {
        logger.debug("Checking in zero-shot human for bizId: {}, checkIn: {}", bizId, checkIn);
        
        CheckInHumanRequest request = new CheckInHumanRequest(bizId, checkIn);
        
        return httpClient.post("/openapi/customize/zero/check-in", request, Boolean.class);
    }
    
    /**
     * 零样本数字人入驻
     * 
     * @param bizId 任务bizId
     * @return 入驻结果
     */
    public BaseResponse<Boolean> checkInZeroShotHuman(String bizId) {
        return checkInZeroShotHuman(bizId, true);
    }
    
    /**
     * 零样本数字人取消入驻
     * 
     * @param bizId 任务bizId
     * @return 取消入驻结果
     */
    public BaseResponse<Boolean> checkOutZeroShotHuman(String bizId) {
        return checkInZeroShotHuman(bizId, false);
    }
    
    // ========== 极速定制声音 APIs ==========
    
    /**
     * 创建零样本声音定制任务
     * 
     * @param request 零样本声音请求
     * @return 任务bizId
     */
    public BaseResponse<String> commitZeroShotVoice(ZeroShotVoiceRequest request) {
        logger.debug("Committing zero-shot voice customization with audioUrl: {}, languageType: {}", 
                    request.getTrainingAudioUrl(), request.getLanguageType());
        
        return httpClient.post("/openapi/customize/zero/voice/commit", request, String.class);
    }
    
    /**
     * 创建零样本声音定制任务 - 简化版本
     * 
     * @param trainingAudioUrl 训练音频地址
     * @param audioText 试听音频文本
     * @param name 声音名称
     * @return 任务bizId
     */
    public BaseResponse<String> commitZeroShotVoice(String trainingAudioUrl, String audioText, String name) {
        ZeroShotVoiceRequest request = new ZeroShotVoiceRequest(trainingAudioUrl);
        request.setAudioText(audioText);
        request.setName(name);
        return commitZeroShotVoice(request);
    }
    
    /**
     * 查询零样本声音训练结果（轮询）
     * 
     * @param bizId 定制任务的bizId
     * @return 训练结果
     */
    public BaseResponse<TrainingResult> getZeroShotVoiceTrainingResult(String bizId) {
        logger.debug("Getting zero-shot voice training result for bizId: {}", bizId);
        
        GetTrainingResultRequest request = new GetTrainingResultRequest(bizId);
        
        return httpClient.post("/openapi/customize/zero/voice/get-training-result", 
                             request, 
                             TrainingResult.class);
    }
}