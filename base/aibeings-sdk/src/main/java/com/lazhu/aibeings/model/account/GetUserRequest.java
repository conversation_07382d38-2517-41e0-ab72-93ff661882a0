package com.lazhu.aibeings.model.account;

import com.lazhu.aibeings.model.BaseRequest;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询单个子账号请求
 */
@Data
@NoArgsConstructor
public class GetUserRequest extends BaseRequest {
    
    /**
     * 用户ID (用户查询参数多选一)
     */
    private String userId;
    
    /**
     * 用户邮箱 (用户查询参数多选一)
     */
    private String userEmail;
    
    /**
     * 用户手机号 (用户查询参数多选一)
     */
    private String userPhone;
    
    // 便捷构造函数
    public GetUserRequest(String userId) {
        this.userId = userId;
    }
    
    // 静态工厂方法
    /**
     * 通过用户ID创建请求
     */
    public static GetUserRequest byUserId(String userId) {
        return new GetUserRequest(userId);
    }
    
    /**
     * 通过用户邮箱创建请求
     */
    public static GetUserRequest byUserEmail(String userEmail) {
        GetUserRequest request = new GetUserRequest();
        request.setUserEmail(userEmail);
        return request;
    }
    
    /**
     * 通过用户手机号创建请求
     */
    public static GetUserRequest byUserPhone(String userPhone) {
        GetUserRequest request = new GetUserRequest();
        request.setUserPhone(userPhone);
        return request;
    }
    
    @Override
    public String toString() {
        return "GetUserRequest{" +
                "userId='" + userId + '\'' +
                ", userEmail='" + userEmail + '\'' +
                ", userPhone='" + userPhone + '\'' +
                '}';
    }
}