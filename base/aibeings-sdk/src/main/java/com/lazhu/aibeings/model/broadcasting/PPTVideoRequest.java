package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lazhu.aibeings.model.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * PPT视频生成请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class PPTVideoRequest extends BaseRequest {
    
    /**
     * 输出视频名称
     */
    @JsonProperty("outputVideoName")
    private String outputVideoName;
    
    /**
     * PPT文件URL
     */
    @JsonProperty("pptUrl")
    private String pptUrl;
    
    /**
     * 虚拟人配置
     */
    @JsonProperty("virtualHuman")
    private VirtualHuman virtualHuman;
    
    /**
     * TTS配置
     */
    @JsonProperty("tts")
    private TTSConfig tts;
    
    /**
     * 讲解文本列表，与PPT页面对应
     */
    @JsonProperty("voiceTexts")
    private List<String> voiceTexts;
    
    /**
     * 背景音乐
     */
    @JsonProperty("backgroundMusic")
    private BackgroundMusic backgroundMusic;
    
    /**
     * 字幕设置
     */
    @JsonProperty("caption")
    private Caption caption;
    
    // 便捷构造函数
    public PPTVideoRequest(String outputVideoName, String pptUrl, VirtualHuman virtualHuman, 
                          TTSConfig tts, List<String> voiceTexts) {
        this.outputVideoName = outputVideoName;
        this.pptUrl = pptUrl;
        this.virtualHuman = virtualHuman;
        this.tts = tts;
        this.voiceTexts = voiceTexts;
    }
    
    /**
     * 设置背景音乐
     */
    public PPTVideoRequest withBackgroundMusic(String musicUrl, double volume) {
        this.backgroundMusic = new BackgroundMusic(musicUrl, volume, true);
        return this;
    }
    
    /**
     * 设置字幕
     */
    public PPTVideoRequest withCaption(int zIndex) {
        this.caption = Caption.defaultCaption(zIndex);
        return this;
    }
    
    /**
     * 验证请求参数
     */
    public boolean isValid() {
        return outputVideoName != null && !outputVideoName.trim().isEmpty()
                && pptUrl != null && !pptUrl.trim().isEmpty()
                && virtualHuman != null && virtualHuman.isValid()
                && tts != null && tts.isValid()
                && voiceTexts != null && !voiceTexts.isEmpty();
    }
}