package com.lazhu.aibeings.model.account;

import com.lazhu.aibeings.model.BaseRequest;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询子账号列表请求
 */
@Data
@NoArgsConstructor
public class UserQueryRequest extends BaseRequest {
    
    /**
     * 页码（从1开始）
     */
    private Integer pageIndex;
    
    /**
     * 每页显示的记录数量
     */
    private Integer pageSize;
    
    /**
     * 组织ID，未开通组织不用填写
     */
    private Integer orgId;
    
    // 便捷构造函数
    public UserQueryRequest(Integer pageIndex, Integer pageSize) {
        this.pageIndex = pageIndex;
        this.pageSize = pageSize;
    }
    
    public UserQueryRequest(Integer pageIndex, Integer pageSize, Integer orgId) {
        this.pageIndex = pageIndex;
        this.pageSize = pageSize;
        this.orgId = orgId;
    }
    
    @Override
    public String toString() {
        return "UserQueryRequest{" +
                "pageIndex=" + pageIndex +
                ", pageSize=" + pageSize +
                ", orgId=" + orgId +
                '}';
    }
}