package com.lazhu.aibeings.model.account;

import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询子账号列表响应
 */
@Data
@NoArgsConstructor
public class UserQueryResponse {
    
    /**
     * 用户列表
     */
    private List<UserVO> list;
    
    /**
     * 当前页码
     */
    private Integer pageIndex;
    
    /**
     * 每页大小
     */
    private Integer pageSize;
    
    /**
     * 总记录数
     */
    private Integer total;
    
    // 便捷构造函数
    public UserQueryResponse(List<UserVO> list, Integer pageIndex, Integer pageSize, Integer total) {
        this.list = list;
        this.pageIndex = pageIndex;
        this.pageSize = pageSize;
        this.total = total;
    }
    
    /**
     * 是否有更多页
     */
    public boolean hasMore() {
        if (pageIndex == null || pageSize == null || total == null) {
            return false;
        }
        return pageIndex * pageSize < total;
    }
    
    /**
     * 获取总页数
     */
    public int getTotalPages() {
        if (pageSize == null || total == null || pageSize <= 0) {
            return 0;
        }
        return (int) Math.ceil((double) total / pageSize);
    }
    
    @Override
    public String toString() {
        return "UserQueryResponse{" +
                "list=" + list +
                ", pageIndex=" + pageIndex +
                ", pageSize=" + pageSize +
                ", total=" + total +
                '}';
    }
}