package com.lazhu.aibeings.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lazhu.aibeings.config.AIBeingsConfig;
import com.lazhu.aibeings.exception.AIBeingsException;
import com.lazhu.aibeings.model.BaseRequest;
import com.lazhu.aibeings.model.BaseResponse;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * HTTP client for AI Beings API
 */
public class HttpClient {
    
    private static final Logger logger = LoggerFactory.getLogger(HttpClient.class);
    
    private final AIBeingsConfig config;
    private final ObjectMapper objectMapper;
    
    public HttpClient(AIBeingsConfig config) {
        this.config = config;
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * Send POST request
     */
    public <T> BaseResponse<T> post(String endpoint, BaseRequest request, Class<T> responseType) {
        try {
            // Convert request to JSON
            String requestBody = objectMapper.writeValueAsString(request);
            
            // Build URL
            String url = config.getBaseUrl() + endpoint;
            
            // Send request with retry logic
            HttpResponse response = sendWithRetry(url, requestBody);
            
            // Parse response
            return parseResponse(response, responseType);
            
        } catch (Exception e) {
            logger.error("Failed to send POST request to {}: {}", endpoint, e.getMessage(), e);
            throw new AIBeingsException("Request failed: " + e.getMessage(), e);
        }
    }
    
    /**
     * Send GET request
     */
    public <T> BaseResponse<T> get(String endpoint, Map<String, Object> params, Class<T> responseType) {
        try {
            // Build URL
            String url = config.getBaseUrl() + endpoint;
            
            // Send request with retry logic
            HttpResponse response = sendGetWithRetry(url, params);
            
            // Parse response
            return parseResponse(response, responseType);
            
        } catch (Exception e) {
            logger.error("Failed to send GET request to {}: {}", endpoint, e.getMessage(), e);
            throw new AIBeingsException("Request failed: " + e.getMessage(), e);
        }
    }
    

    
    private HttpResponse sendWithRetry(String url, String requestBody) {
        Exception lastException = null;
        
        for (int i = 0; i < config.getMaxRetries(); i++) {
            try {
                HttpRequest httpRequest = HttpUtil.createPost(url)
                    .header("Content-Type", "application/json")
                    .header("subscription-key", config.getSubscriptionKey())
                    .timeout(config.getConnectTimeout())
                    .body(requestBody);
                
                HttpResponse response = httpRequest.execute();
                
                if (response.getStatus() == 200) {
                    return response;
                } else if (shouldRetry(response.getStatus()) && i < config.getMaxRetries() - 1) {
                    // Retry on server errors or rate limiting
                    logger.warn("Retryable error ({}), retrying... ({}/{})", response.getStatus(), i + 1, config.getMaxRetries());
                    Thread.sleep(calculateBackoffDelay(i, response.getStatus()));
                    continue;
                } else {
                    // Handle specific HTTP status codes according to AI Beings documentation
                    throw createHttpException(response);
                }
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new AIBeingsException("Request interrupted", e);
            } catch (AIBeingsException e) {
                throw e;
            } catch (Exception e) {
                lastException = e;
                if (i < config.getMaxRetries() - 1) {
                    logger.warn("Request failed, retrying... ({}/{}): {}", i + 1, config.getMaxRetries(), e.getMessage());
                    try {
                        Thread.sleep(1000 * (i + 1));
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new AIBeingsException("Request interrupted", ie);
                    }
                }
            }
        }
        
        throw new AIBeingsException("Request failed after " + config.getMaxRetries() + " retries", lastException);
    }
    
    private HttpResponse sendGetWithRetry(String url, Map<String, Object> params) {
        Exception lastException = null;
        
        for (int i = 0; i < config.getMaxRetries(); i++) {
            try {
                HttpRequest httpRequest = HttpUtil.createGet(url)
                    .header("subscription-key", config.getSubscriptionKey())
                    .timeout(config.getConnectTimeout())
                    .form(params);
                
                HttpResponse response = httpRequest.execute();
                
                if (response.getStatus() == 200) {
                    return response;
                } else if (shouldRetry(response.getStatus()) && i < config.getMaxRetries() - 1) {
                    // Retry on server errors or rate limiting
                    logger.warn("Retryable error ({}), retrying... ({}/{})", response.getStatus(), i + 1, config.getMaxRetries());
                    Thread.sleep(calculateBackoffDelay(i, response.getStatus()));
                    continue;
                } else {
                    // Handle specific HTTP status codes according to AI Beings documentation
                    throw createHttpException(response);
                }
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new AIBeingsException("Request interrupted", e);
            } catch (AIBeingsException e) {
                throw e;
            } catch (Exception e) {
                lastException = e;
                if (i < config.getMaxRetries() - 1) {
                    logger.warn("Request failed, retrying... ({}/{}): {}", i + 1, config.getMaxRetries(), e.getMessage());
                    try {
                        Thread.sleep(1000 * (i + 1));
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new AIBeingsException("Request interrupted", ie);
                    }
                }
            }
        }
        
        throw new AIBeingsException("Request failed after " + config.getMaxRetries() + " retries", lastException);
    }
    
    /**
     * Check if the HTTP status code should trigger a retry
     */
    private boolean shouldRetry(int statusCode) {
        return statusCode >= 500 || statusCode == 429; // Server errors or rate limiting
    }
    
    /**
     * Calculate backoff delay based on retry attempt and status code
     */
    private long calculateBackoffDelay(int retryAttempt, int statusCode) {
        if (statusCode == 429) {
            // For rate limiting, use longer delays
            return Math.min(30000, 2000 * (retryAttempt + 1)); // Max 30 seconds
        } else {
            // For server errors, use exponential backoff
            return 1000 * (retryAttempt + 1);
        }
    }
    
    /**
     * Create appropriate exception based on HTTP status code
     */
    private AIBeingsException createHttpException(HttpResponse response) {
        int statusCode = response.getStatus();
        String responseBody = response.body();
        
        switch (statusCode) {
            case 400:
                return new AIBeingsException(1001, "输入缺少必要参数: " + responseBody);
            case 401:
                return new AIBeingsException(statusCode, "未授权，缺少订阅key或订阅key失效: " + responseBody);
            case 404:
                return new AIBeingsException(statusCode, "访问的资源不存在: " + responseBody);
            case 429:
                if (responseBody.contains("Rate limit")) {
                    return new AIBeingsException(statusCode, "接口请求频率过高，被限流: " + responseBody);
                } else {
                    return new AIBeingsException(1003, "超过业务层接口允许的测试调用次数: " + responseBody);
                }
            case 500:
                return new AIBeingsException(0, "API服务器内部错误，请联系相关开发人员: " + responseBody);
            default:
                return new AIBeingsException(statusCode, "HTTP错误 " + statusCode + ": " + responseBody);
        }
    }
    
    @SuppressWarnings("unchecked")
    private <T> BaseResponse<T> parseResponse(HttpResponse response, Class<T> responseType) {
        try {
            String responseBody = response.body();
            
            if (StrUtil.isBlank(responseBody)) {
                throw new AIBeingsException("Empty response body");
            }
            
            // Parse as BaseResponse
            BaseResponse<T> baseResponse = objectMapper.readValue(responseBody, 
                objectMapper.getTypeFactory().constructParametricType(BaseResponse.class, responseType));
            
            // Check business logic status codes
            if (!baseResponse.isSuccess()) {
                throw createBusinessException(baseResponse);
            }
            
            return baseResponse;
            
        } catch (AIBeingsException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Failed to parse response: {}", e.getMessage(), e);
            throw new AIBeingsException("Failed to parse response: " + e.getMessage(), e);
        }
    }
    
    /**
     * Create appropriate exception based on business logic code
     */
    private AIBeingsException createBusinessException(BaseResponse<?> response) {
        Integer code = response.getCode();
        String message = response.getMessage();
        String traceId = response.getTraceId();
        
        if (code == null) {
            return new AIBeingsException("Unknown error: " + message, traceId);
        }
        
        switch (code) {
            case 200:
                // This shouldn't happen as isSuccess() should return true
                return new AIBeingsException(code, "操作成功: " + message, traceId);
            case 407:
                return new AIBeingsException(code, "参数校验失败，请检测输入: " + message, traceId);
            case 1002:
                return new AIBeingsException(code, "subscription-key错误: " + message, traceId);
            case 1011001:
                return new AIBeingsException(code, "权益不足，需要购买定制权益: " + message, traceId);
            case 1021001:
                return new AIBeingsException(code, "训练视频有问题，预处理失败，请检查录制的视频格式、长度、音量等: " + message, traceId);
            case 1:
                return new AIBeingsException(code, "系统错误，操作失败，请联系小冰研发排查问题: " + message, traceId);
            default:
                return new AIBeingsException(code, message != null ? message : "未知错误", traceId);
        }
    }
}