package com.lazhu.aibeings.exception;

/**
 * AI Beings SDK Exception
 */
public class AIBeingsException extends RuntimeException {
    
    private Integer errorCode;
    private String errorMessage;
    private String requestId;
    
    public AIBeingsException(String message) {
        super(message);
        this.errorMessage = message;
    }
    
    public AIBeingsException(Integer errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.errorMessage = message;
    }
    
    public AIBeingsException(Integer errorCode, String message, String requestId) {
        super(message);
        this.errorCode = errorCode;
        this.errorMessage = message;
        this.requestId = requestId;
    }
    
    public AIBeingsException(String message, String requestId) {
        super(message);
        this.errorMessage = message;
        this.requestId = requestId;
    }
    
    public AIBeingsException(String message, Throwable cause) {
        super(message, cause);
        this.errorMessage = message;
    }
    
    public AIBeingsException(Integer errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorMessage = message;
    }
    
    // Getters
    public Integer getErrorCode() {
        return errorCode;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public String getRequestId() {
        return requestId;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("AIBeingsException{");
        if (errorCode != null) {
            sb.append("errorCode=").append(errorCode).append(", ");
        }
        sb.append("errorMessage='").append(errorMessage).append("'");
        if (requestId != null) {
            sb.append(", requestId='").append(requestId).append("'");
        }
        sb.append("}");
        return sb.toString();
    }
}