package com.lazhu.aibeings.model.subscription;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 订阅信息模型
 */
@Data
@NoArgsConstructor
public class SubscriptionInfo {
    
    @JsonProperty("tenantOpenId")
    private String tenantOpenId;
    
    @JsonProperty("appId")
    private Integer appId;
    
    @JsonProperty("tenantSubscriptionId")
    private String tenantSubscriptionId;
    
    @JsonProperty("subscriptionName")
    private String subscriptionName;
    
    @JsonProperty("subscriptionDuration")
    private Integer subscriptionDuration;
    
    @JsonProperty("subscriptionDurationType")
    private String subscriptionDurationType; // day, month, year
    
    @JsonProperty("status")
    private String status; // valid: 有效, expired: 已过期
    
    @JsonProperty("adaptiveEndTime")
    private String adaptiveEndTime;
    
    @JsonProperty("createTime")
    private String createTime;
    
    @JsonProperty("total")
    private Integer total;
    
    @JsonProperty("resources")
    private List<SubscriptionResource> resources;
    

    
    /**
     * 是否为有效订阅
     */
    public boolean isValid() {
        return "valid".equals(status);
    }
    
    /**
     * 是否已过期
     */
    public boolean isExpired() {
        return "expired".equals(status);
    }
    
    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        if (isValid()) {
            return "有效";
        } else if (isExpired()) {
            return "已过期";
        } else {
            return status != null ? status : "未知";
        }
    }
    
    /**
     * 获取持续时间描述
     */
    public String getDurationDescription() {
        if (subscriptionDuration == null || subscriptionDurationType == null) {
            return "未知";
        }
        
        String unit;
        switch (subscriptionDurationType.toLowerCase()) {
            case "day":
                unit = "天";
                break;
            case "month":
                unit = "月";
                break;
            case "year":
                unit = "年";
                break;
            default:
                unit = subscriptionDurationType;
        }
        
        return subscriptionDuration + unit;
    }
    
    /**
     * 获取格式化的结束时间
     */
    public String getFormattedEndTime() {
        if (adaptiveEndTime == null) {
            return "未知";
        }
        
        try {
            LocalDateTime dateTime = LocalDateTime.parse(adaptiveEndTime, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            return adaptiveEndTime;
        }
    }
    
    /**
     * 获取格式化的创建时间
     */
    public String getFormattedCreateTime() {
        if (createTime == null) {
            return "未知";
        }
        
        try {
            LocalDateTime dateTime = LocalDateTime.parse(createTime, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            return createTime;
        }
    }
}