package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;

/**
 * 视频场景
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VideoScene {
    
    /**
     * 虚拟人信息
     */
    @JsonProperty("virtualHuman")
    private VirtualHuman virtualHuman;
    
    /**
     * TTS声音信息
     */
    @JsonProperty("tts")
    private TTSConfig tts;
    
    /**
     * 自定义语音的音频URL
     */
    @JsonProperty("voiceUrl")
    private String voiceUrl;
    
    /**
     * 场景背景音乐信息
     */
    @JsonProperty("backgroundMusic")
    private BackgroundMusic backgroundMusic;
    
    /**
     * 背景图片
     */
    @JsonProperty("backgroundImage")
    private BackgroundImage backgroundImage;
    
    /**
     * 本段场景内将要演播的内容
     */
    @JsonProperty("voiceTexts")
    private List<String> voiceTexts;
    
    /**
     * 本段场景内展示的文本素材
     */
    @JsonProperty("displayTexts")
    private List<DisplayText> displayTexts;
    
    /**
     * 本段场景内展示的图片
     */
    @JsonProperty("displayImages")
    private List<DisplayImage> displayImages;
    
    /**
     * 本段场景内展示的视频
     */
    @JsonProperty("displayVideos")
    private List<DisplayVideo> displayVideos;
    
    /**
     * 字幕设置
     */
    @JsonProperty("caption")
    private Caption caption;
    
    /**
     * 转场动画
     */
    @JsonProperty("transferEffects")
    private List<TransferEffect> transferEffects;
    

    
    /**
     * 使用TTS驱动
     */
    public VideoScene withTTS(String voiceId, List<String> texts) {
        this.tts = new TTSConfig(voiceId);
        this.voiceTexts = texts;
        return this;
    }
    
    /**
     * 使用音频URL驱动
     */
    public VideoScene withVoiceUrl(String voiceUrl) {
        this.voiceUrl = voiceUrl;
        return this;
    }
    
    /**
     * 设置背景图片
     */
    public VideoScene withBackgroundImage(String imageUrl) {
        this.backgroundImage = new BackgroundImage(imageUrl);
        return this;
    }
    
    /**
     * 验证场景配置
     */
    public boolean isValid() {
        if (virtualHuman == null) {
            return false;
        }
        
        // TTS和音频URL二选一
        boolean hasTTS = tts != null && voiceTexts != null && !voiceTexts.isEmpty();
        boolean hasVoiceUrl = voiceUrl != null && !voiceUrl.trim().isEmpty();
        
        return hasTTS || hasVoiceUrl;
    }
}