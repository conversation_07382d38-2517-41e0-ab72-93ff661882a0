package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TTS音频生成响应
 */
@Data
@NoArgsConstructor
public class TTSAudioResponse {
    
    /**
     * 音频文件URL
     */
    @JsonProperty("audioUrl")
    private String audioUrl;
    
    /**
     * 音频时长（秒）
     */
    @JsonProperty("duration")
    private Double duration;
    
    /**
     * 音频大小（字节）
     */
    @JsonProperty("audioSizeBytes")
    private Long audioSizeBytes;
    
    /**
     * 音频格式
     */
    @JsonProperty("format")
    private String format;
    
    /**
     * 任务ID（如果是异步处理）
     */
    @JsonProperty("taskId")
    private String taskId;
    
    /**
     * 状态（如果是异步处理）
     */
    @JsonProperty("status")
    private String status;
    

    
    /**
     * 获取音频大小（MB）
     */
    public Double getAudioSizeMB() {
        if (audioSizeBytes == null) {
            return null;
        }
        return audioSizeBytes / (1024.0 * 1024.0);
    }
    
    /**
     * 获取时长描述
     */
    public String getDurationDescription() {
        if (duration == null) {
            return "未知";
        }
        
        int minutes = (int) (duration / 60);
        int seconds = (int) (duration % 60);
        
        if (minutes > 0) {
            return String.format("%d分%d秒", minutes, seconds);
        } else {
            return String.format("%d秒", seconds);
        }
    }
    
    /**
     * 是否生成完成
     */
    public boolean isCompleted() {
        return audioUrl != null && !audioUrl.trim().isEmpty();
    }
}