package com.lazhu.aibeings.model.customize;

import com.lazhu.aibeings.model.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 创建零样本声音定制任务请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class ZeroShotVoiceRequest extends BaseRequest {
    
    /**
     * 声音的名称，默认为账号名字，最大长度16字符
     */
    private String name;
    
    /**
     * 训练音频
     * 普通话定制音频时长需在10-30s，多语言定制音频时长需在10s-3min
     * 支持的声音格式："wav", "m4a", "mp3", "ogg"
     */
    private String trainingAudioUrl;
    
    /**
     * 生成试听音频的文本
     * 注：该文本不支持传入SSML标签，声音定制完成后支持SSML标签文本生成
     */
    private String audioText;
    
    /**
     * 指定语言类型，CHINESE 普通话，MULTI 多语言；默认普通话
     */
    private String languageType;
    
    /**
     * 是否消除背景噪音（仅对MULTI 多语言定制有效）
     * 0 否，1 是；默认 否
     */
    private Integer reduceNoise;
    
    // 便捷构造函数
    public ZeroShotVoiceRequest(String trainingAudioUrl) {
        this.trainingAudioUrl = trainingAudioUrl;
    }
    
    /**
     * 语言类型枚举
     */
    public enum LanguageType {
        CHINESE("CHINESE", "普通话"),
        MULTI("MULTI", "多语言");
        
        private final String code;
        private final String description;
        
        LanguageType(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    @Override
    public String toString() {
        return "ZeroShotVoiceRequest{" +
                "name='" + name + '\'' +
                ", trainingAudioUrl='" + trainingAudioUrl + '\'' +
                ", audioText='" + audioText + '\'' +
                ", languageType='" + languageType + '\'' +
                ", reduceNoise=" + reduceNoise +
                '}';
    }
}