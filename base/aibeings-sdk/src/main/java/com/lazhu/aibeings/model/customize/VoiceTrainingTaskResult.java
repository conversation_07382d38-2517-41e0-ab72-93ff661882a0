package com.lazhu.aibeings.model.customize;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 声音训练任务结果
 */
@Data
@NoArgsConstructor
public class VoiceTrainingTaskResult {
    
    /**
     * 训练阶段
     */
    private String stage;
    
    /**
     * 进度 1-100
     */
    private Integer progress;
    
    /**
     * 失败原因
     */
    private String message;
    
    /**
     * 用于配置数字人的声音bizId
     */
    private String voiceBizId;
    
    /**
     * 用于合成播报视频、音频的声音ID
     */
    private String voiceOpenapiId;
    

    
    /**
     * 训练阶段枚举
     */
    public enum Stage {
        WAITING("WAITING", "排队中"),
        TRAINING("TRAINING", "训练中"),
        SUCCEED("SUCCEED", "训练完成，可用于合成播报视频、音频");
        
        private final String code;
        private final String description;
        
        Stage(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 是否训练完成
     */
    public boolean isCompleted() {
        return Stage.SUCCEED.getCode().equals(stage);
    }
    
    /**
     * 是否正在进行中
     */
    public boolean isInProgress() {
        return Stage.WAITING.getCode().equals(stage) || Stage.TRAINING.getCode().equals(stage);
    }
    
    @Override
    public String toString() {
        return "VoiceTrainingTaskResult{" +
                "stage='" + stage + '\'' +
                ", progress=" + progress +
                ", message='" + message + '\'' +
                ", voiceBizId='" + voiceBizId + '\'' +
                ", voiceOpenapiId='" + voiceOpenapiId + '\'' +
                '}';
    }
}