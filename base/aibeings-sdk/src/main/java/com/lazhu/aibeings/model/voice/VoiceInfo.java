package com.lazhu.aibeings.model.voice;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 声音信息模型
 */
@Data
@NoArgsConstructor
public class VoiceInfo {
    
    @JsonProperty("bizId")
    private String bizId;
    
    @JsonProperty("voiceId")
    private String voiceId;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("language")
    private String language;
    
    @JsonProperty("previewImageUrl")
    private String previewImageUrl;
    
    @JsonProperty("auditionFile")
    private String auditionFile;
    
    @JsonProperty("supportInteractive")
    private Boolean supportInteractive;
    

    
    /**
     * 是否支持交互
     */
    public boolean isInteractiveSupported() {
        return supportInteractive != null && supportInteractive;
    }
    
    /**
     * 获取语言描述
     */
    public String getLanguageDescription() {
        if (language == null) {
            return "未知";
        }
        
        switch (language.toLowerCase()) {
            case "zh-cn":
            case "zh-chs":
                return "中文";
            case "en-us":
                return "英语(美国)";
            case "en-gb":
                return "英语(英国)";
            case "ja-jp":
                return "日语";
            case "ko-kr":
                return "韩语";
            default:
                return language;
        }
    }
}