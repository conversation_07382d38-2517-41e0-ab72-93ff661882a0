package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lazhu.aibeings.model.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 视频生成请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class VideoCreationRequest extends BaseRequest {
    
    /**
     * 输出视频名称
     */
    @JsonProperty("outputVideoName")
    private String outputVideoName;
    
    /**
     * 输出视频格式，支持mkv，mp4，默认mp4
     */
    @JsonProperty("outputVideoFormat")
    private String outputVideoFormat = "mp4";
    
    /**
     * 视频宽度，默认1920
     */
    @JsonProperty("width")
    private Integer width = 1920;
    
    /**
     * 视频高度，默认1080
     */
    @JsonProperty("height")
    private Integer height = 1080;
    
    /**
     * 生成详细配置
     */
    @JsonProperty("creationDetail")
    private VideoCreationDetail creationDetail;
    

    
    /**
     * 设置视频尺寸
     */
    public VideoCreationRequest withDimensions(int width, int height) {
        this.width = width;
        this.height = height;
        return this;
    }
    
    /**
     * 设置视频格式
     */
    public VideoCreationRequest withFormat(String format) {
        this.outputVideoFormat = format;
        return this;
    }
    
    /**
     * 验证请求参数
     */
    public boolean isValid() {
        if (outputVideoName == null || outputVideoName.trim().isEmpty()) {
            return false;
        }
        if (outputVideoName.length() > 100) {
            return false;
        }
        if (width != null && height != null && width * height > 3840 * 2160) {
            return false;
        }
        if (width != null && width % 2 != 0) {
            return false;
        }
        if (height != null && height % 2 != 0) {
            return false;
        }
        return creationDetail != null;
    }
}