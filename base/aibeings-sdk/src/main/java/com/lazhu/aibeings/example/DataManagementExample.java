package com.lazhu.aibeings.example;

import com.lazhu.aibeings.AIBeingsClient;
import com.lazhu.aibeings.model.BaseResponse;
import com.lazhu.aibeings.model.employee.DigitalEmployee;
import com.lazhu.aibeings.model.employee.DigitalEmployeeRequest;
import com.lazhu.aibeings.model.employee.DigitalEmployeeResponse;
import com.lazhu.aibeings.model.resource.ResourceInfo;
import com.lazhu.aibeings.model.subscription.SubscriptionInfo;
import com.lazhu.aibeings.model.subscription.SubscriptionResponse;
import com.lazhu.aibeings.model.voice.VoiceInfo;

import java.util.Arrays;
import java.util.List;

/**
 * 基础数据管理示例
 */
public class DataManagementExample {
    
    private static final String SUBSCRIPTION_KEY = "your_subscription_key_here";
    
    public static void main(String[] args) {
        // 初始化客户端
        AIBeingsClient client = AIBeingsClient.builder()
            .subscriptionKey(SUBSCRIPTION_KEY)
            .build();
        
        // 示例1: 查询数字员工列表
        queryDigitalEmployees(client);
        
        // 示例2: 搜索数字员工
        searchDigitalEmployees(client);
        
        // 示例3: 查询不同分类的数字员工
        queryEmployeesByCategory(client);
        
        // 示例4: 查询声音列表
        queryVoiceList(client);
        
        // 示例5: 筛选声音
        filterVoices(client);
        
        // 示例6: 查询企业权益数据
        queryEnterpriseResources(client);
        
        // 示例7: 查询订阅记录
        querySubscriptions(client);
    }
    
    /**
     * 查询数字员工列表示例
     */
    private static void queryDigitalEmployees(AIBeingsClient client) {
        System.out.println("=== 查询数字员工列表示例 ===");
        
        try {
            // 查询播报场景的数字员工
            BaseResponse<DigitalEmployeeResponse> response = client.dataManagement()
                .queryStudioEmployees(1, 10);
            
            if (response.isSuccess()) {
                DigitalEmployeeResponse data = response.getData();
                System.out.println("总数: " + data.getTotal());
                System.out.println("当前页: " + data.getCurrent() + "/" + data.getPages());
                
                for (DigitalEmployee employee : data.getRecords()) {
                    System.out.println("员工ID: " + employee.getBizId());
                    System.out.println("姓名: " + employee.getName());
                    System.out.println("分类: " + employee.getCategoryDescription());
                    System.out.println("行业: " + employee.getIndustry());
                    System.out.println("语言: " + employee.getLanguage());
                    System.out.println("介绍: " + employee.getIntroduce());
                    System.out.println("---");
                }
            } else {
                System.err.println("查询失败: " + response.getMessage());
            }
            
        } catch (Exception e) {
            System.err.println("错误: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 搜索数字员工示例
     */
    private static void searchDigitalEmployees(AIBeingsClient client) {
        System.out.println("=== 搜索数字员工示例 ===");
        
        try {
            String keyword = "销售";
            BaseResponse<DigitalEmployeeResponse> response = client.dataManagement()
                .searchDigitalEmployees(keyword, 
                    DigitalEmployeeRequest.ModelType.STUDIO.getCode(), 1, 5);
            
            if (response.isSuccess()) {
                DigitalEmployeeResponse data = response.getData();
                System.out.println("搜索关键字: " + keyword);
                System.out.println("找到 " + data.getTotal() + " 个结果");
                
                for (DigitalEmployee employee : data.getRecords()) {
                    System.out.println("姓名: " + employee.getName());
                    System.out.println("行业: " + employee.getIndustry());
                    System.out.println("介绍: " + employee.getIntroduce());
                    System.out.println("---");
                }
            } else {
                System.err.println("搜索失败: " + response.getMessage());
            }
            
        } catch (Exception e) {
            System.err.println("错误: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 按分类查询数字员工示例
     */
    private static void queryEmployeesByCategory(AIBeingsClient client) {
        System.out.println("=== 按分类查询数字员工示例 ===");
        
        try {
            // 查询官方精品员工
            BaseResponse<DigitalEmployeeResponse> response = client.dataManagement()
                .queryOfficialEmployees(DigitalEmployeeRequest.ModelType.INTERACTIVE.getCode(), 1, 5);
            
            if (response.isSuccess()) {
                DigitalEmployeeResponse data = response.getData();
                System.out.println("官方精品员工 (交互场景):");
                System.out.println("总数: " + data.getTotal());
                
                for (DigitalEmployee employee : data.getRecords()) {
                    System.out.println("姓名: " + employee.getName());
                    System.out.println("分类: " + employee.getCategoryDescription());
                    System.out.println("经验: " + employee.getExperience());
                    System.out.println("---");
                }
            } else {
                System.err.println("查询失败: " + response.getMessage());
            }
            
        } catch (Exception e) {
            System.err.println("错误: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 查询声音列表示例
     */
    private static void queryVoiceList(AIBeingsClient client) {
        System.out.println("=== 查询声音列表示例 ===");
        
        try {
            BaseResponse<List<VoiceInfo>> response = client.dataManagement().queryVoiceList();
            
            if (response.isSuccess()) {
                List<VoiceInfo> voices = response.getData();
                System.out.println("可用声音总数: " + voices.size());
                
                // 显示前5个声音
                voices.stream().limit(5).forEach(voice -> {
                    System.out.println("声音ID: " + voice.getVoiceId());
                    System.out.println("名称: " + voice.getName());
                    System.out.println("语言: " + voice.getLanguageDescription());
                    System.out.println("支持交互: " + (voice.isInteractiveSupported() ? "是" : "否"));
                    System.out.println("试听地址: " + voice.getAuditionFile());
                    System.out.println("---");
                });
                
            } else {
                System.err.println("查询失败: " + response.getMessage());
            }
            
        } catch (Exception e) {
            System.err.println("错误: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 筛选声音示例
     */
    private static void filterVoices(AIBeingsClient client) {
        System.out.println("=== 筛选声音示例 ===");
        
        try {
            // 筛选中文声音
            List<VoiceInfo> chineseVoices = client.dataManagement()
                .filterVoicesByLanguage("zh-CN");
            
            System.out.println("中文声音数量: " + chineseVoices.size());
            
            // 获取支持交互的声音
            List<VoiceInfo> interactiveVoices = client.dataManagement()
                .getInteractiveSupportedVoices();
            
            System.out.println("支持交互的声音数量: " + interactiveVoices.size());
            
            // 显示支持交互的中文声音
            System.out.println("\n支持交互的中文声音:");
            chineseVoices.stream()
                .filter(VoiceInfo::isInteractiveSupported)
                .limit(3)
                .forEach(voice -> {
                    System.out.println("- " + voice.getName() + " (" + voice.getVoiceId() + ")");
                });
            
        } catch (Exception e) {
            System.err.println("错误: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 查询企业权益数据示例
     */
    private static void queryEnterpriseResources(AIBeingsClient client) {
        System.out.println("=== 查询企业权益数据示例 ===");
        
        try {
            BaseResponse<List<ResourceInfo>> response = client.dataManagement().queryEnterpriseResources();
            
            if (response.isSuccess()) {
                List<ResourceInfo> resources = response.getData();
                System.out.println("企业权益总数: " + resources.size());
                
                // 显示前10个权益
                resources.stream().limit(10).forEach(resource -> {
                    System.out.println("权益编码: " + resource.getResourceCode());
                    System.out.println("权益名称: " + resource.getResourceName());
                    System.out.println("权益类型: " + (resource.isRentalType() ? "租赁型" : "消耗型"));
                    System.out.println("总量: " + resource.getReadableTotal());
                    System.out.println("已使用: " + resource.getReadableUsed());
                    System.out.println("剩余: " + resource.getReadableRemaining());
                    System.out.println("使用率: " + String.format("%.1f%%", resource.getUsagePercentage()));
                    System.out.println("---");
                });
                
                // 统计租赁型和消耗型资源
                List<ResourceInfo> rentalResources = client.dataManagement().getRentalResources();
                List<ResourceInfo> consumeResources = client.dataManagement().getConsumeResources();
                
                System.out.println("\n资源统计:");
                System.out.println("租赁型资源: " + rentalResources.size() + " 个");
                System.out.println("消耗型资源: " + consumeResources.size() + " 个");
                
            } else {
                System.err.println("查询失败: " + response.getMessage());
            }
            
        } catch (Exception e) {
            System.err.println("错误: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 查询订阅记录示例
     */
    private static void querySubscriptions(AIBeingsClient client) {
        System.out.println("=== 查询订阅记录示例 ===");
        
        try {
            // 查询License订阅记录
            BaseResponse<SubscriptionResponse> licenseResponse = client.dataManagement()
                .queryLicenseSubscriptions(1, 10);
            
            if (licenseResponse.isSuccess()) {
                SubscriptionResponse licenseData = licenseResponse.getData();
                System.out.println("License订阅记录:");
                System.out.println("总数: " + licenseData.getTotal());
                
                for (SubscriptionInfo subscription : licenseData.getRecords()) {
                    System.out.println("订阅名称: " + subscription.getSubscriptionName());
                    System.out.println("状态: " + subscription.getStatusDescription());
                    System.out.println("有效期: " + subscription.getDurationDescription());
                    System.out.println("到期时间: " + subscription.getFormattedEndTime());
                    System.out.println("包含权益数: " + (subscription.getResources() != null ? subscription.getResources().size() : 0));
                    System.out.println("---");
                }
            }
            
            // 查询资源包订阅记录
            BaseResponse<SubscriptionResponse> packageResponse = client.dataManagement()
                .queryPackageSubscriptions(1, 10);
            
            if (packageResponse.isSuccess()) {
                SubscriptionResponse packageData = packageResponse.getData();
                System.out.println("\n资源包订阅记录:");
                System.out.println("总数: " + packageData.getTotal());
                
                for (SubscriptionInfo subscription : packageData.getRecords()) {
                    System.out.println("订阅名称: " + subscription.getSubscriptionName());
                    System.out.println("状态: " + subscription.getStatusDescription());
                    System.out.println("有效期: " + subscription.getDurationDescription());
                    System.out.println("到期时间: " + subscription.getFormattedEndTime());
                    System.out.println("---");
                }
            }
            
            // 获取所有有效订阅
            List<SubscriptionInfo> validSubscriptions = client.dataManagement().getValidSubscriptions();
            System.out.println("\n有效订阅总数: " + validSubscriptions.size());
            
        } catch (Exception e) {
            System.err.println("错误: " + e.getMessage());
        }
        
        System.out.println();
    }
}