package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;

/**
 * 视频生成详细配置
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VideoCreationDetail {
    
    /**
     * 全局背景音乐
     */
    @JsonProperty("backgroundMusic")
    private BackgroundMusic backgroundMusic;
    
    /**
     * 视频场景列表
     */
    @JsonProperty("scenes")
    private List<VideoScene> scenes;
    

    
    /**
     * 添加场景
     */
    public VideoCreationDetail addScene(VideoScene scene) {
        if (this.scenes != null) {
            this.scenes.add(scene);
        }
        return this;
    }
    
    /**
     * 设置全局背景音乐
     */
    public VideoCreationDetail withBackgroundMusic(String mediaUrl, Double volume, Boolean loop) {
        this.backgroundMusic = new BackgroundMusic(mediaUrl, volume, loop);
        return this;
    }
    
    /**
     * 验证配置
     */
    public boolean isValid() {
        return scenes != null && !scenes.isEmpty();
    }
}