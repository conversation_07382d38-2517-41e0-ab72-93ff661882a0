package com.lazhu.aibeings.example;

import com.lazhu.aibeings.AIBeingsClient;
import com.lazhu.aibeings.model.BaseResponse;
import com.lazhu.aibeings.model.broadcasting.*;

import java.util.Arrays;
import java.util.List;

/**
 * 数字人播报功能示例
 */
public class BroadcastingExample {
    
    private static final String SUBSCRIPTION_KEY = "your_subscription_key_here";
    
    public static void main(String[] args) {
        AIBeingsClient client = new AIBeingsClient(SUBSCRIPTION_KEY);
        
        // 示例1: 查询数字员工详情
        queryDigitalEmployeeExample(client);
        
        // 示例2: 创建编辑器模式视频
        createEditorModeVideoExample(client);
        
        // 示例3: 创建PPT讲解视频
        createPPTVideoExample(client);
        
        // 示例4: 创建透明数字人视频
        createTransparentVideoExample(client);
        
        // 示例5: 生成TTS音频
        generateTTSAudioExample(client);
        
        // 示例6: 查询视频任务列表
        queryVideoTasksExample(client);
    }
    
    /**
     * 查询数字员工详情示例
     */
    public static void queryDigitalEmployeeExample(AIBeingsClient client) {
        System.out.println("=== 查询数字员工详情示例 ===");
        
        try {
            String bizId = "VHPPAMMZ4"; // 数字员工bizId
            BaseResponse<DigitalEmployeeDetail> response = client.broadcasting()
                .getDigitalEmployeeDetail(bizId);
            
            if (response.isSuccess()) {
                DigitalEmployeeDetail detail = response.getData();
                System.out.println("数字员工ID: " + detail.getVirtualHumanId());
                System.out.println("默认声音ID: " + detail.getDefaultVoiceId());
                System.out.println("员工类型: " + detail.getCategoryDescription());
                System.out.println("支持透明视频: " + detail.getSupportTransparency());
                System.out.println("支持的声音数量: " + detail.getVoiceInfos().size());
                System.out.println("支持的动作数量: " + detail.getPostureInfos().size());
                
                // 显示第一个声音信息
                if (!detail.getVoiceInfos().isEmpty()) {
                    VoiceInfo voice = detail.getVoiceInfos().get(0);
                    System.out.println("第一个声音: " + voice.getDisplayName() + 
                                     " (" + voice.getLanguage() + ", " + voice.getGender() + ")");
                }
                
                // 显示第一个动作信息
                if (!detail.getPostureInfos().isEmpty()) {
                    PostureInfo posture = detail.getPostureInfos().get(0);
                    System.out.println("第一个动作: " + posture.getName() + " (ID: " + posture.getBizId() + ")");
                }
            } else {
                System.err.println("查询失败: " + response.getMessage());
            }
        } catch (Exception e) {
            System.err.println("查询异常: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 创建编辑器模式视频示例
     */
    public static void createEditorModeVideoExample(AIBeingsClient client) {
        System.out.println("=== 创建编辑器模式视频示例 ===");
        
        try {
            // 1. 创建虚拟人配置
            VirtualHumanAttributes attributes = new VirtualHumanAttributes(792, 1712, 1500, 500);
            VirtualHuman virtualHuman = new VirtualHuman(
                "80SdjjACwdZR73tRlZFYsg", // 虚拟人ID
                "n0UcBeHCw3xK",           // 姿势ID
                20,                       // 图层
                attributes
            );
            
            // 2. 创建TTS配置
            TTSConfig tts = new TTSConfig("181-hanfangziran")
                .withRate(1.0f)
                .withPitch(1.0f)
                .withVolume(50);
            
            // 3. 创建场景
            List<String> voiceTexts = Arrays.asList(
                "欢迎来到AI数字人播报系统",
                "我是您的专属数字主播",
                "今天为您带来最新的科技资讯"
            );
            
            VideoScene scene = new VideoScene();
            scene.setVirtualHuman(virtualHuman);
            scene.setVoiceTexts(voiceTexts);
            scene.setTts(tts);
            
            // 设置背景图片
            scene.setBackgroundImage(new BackgroundImage(
                "https://example.com/background.jpg"));
            
            // 设置字幕
            Caption caption = Caption.defaultCaption(60).center();
            scene.setCaption(caption);
            
            // 添加转场效果
            scene.setTransferEffects(Arrays.asList(
                TransferEffect.wipeLeft(3.0f)
            ));
            
            // 4. 创建视频生成详细配置
            VideoCreationDetail creationDetail = new VideoCreationDetail();
            creationDetail.setScenes(Arrays.asList(scene));
            
            // 设置全局背景音乐
            creationDetail.setBackgroundMusic(new BackgroundMusic(
                "https://example.com/background-music.mp3", 0.3, true));
            
            // 5. 创建视频生成请求
            VideoCreationRequest request = new VideoCreationRequest();
            request.setOutputVideoName("AI数字人播报视频");
            request.setCreationDetail(creationDetail);
            request.setWidth(1920);
            request.setHeight(1080);
            request.setOutputVideoFormat("mp4");
            
            // 6. 提交视频生成任务
            BaseResponse<VideoTaskResponse> response = client.broadcasting()
                .createEditorModeVideo(request);
            
            if (response.isSuccess()) {
                String taskId = response.getData().getId();
                System.out.println("视频生成任务已提交，任务ID: " + taskId);
                
                // 7. 轮询查询任务状态
                pollTaskStatus(client, taskId);
            } else {
                System.err.println("视频生成失败: " + response.getMessage());
            }
        } catch (Exception e) {
            System.err.println("视频生成异常: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 创建PPT讲解视频示例
     */
    public static void createPPTVideoExample(AIBeingsClient client) {
        System.out.println("=== 创建PPT讲解视频示例 ===");
        
        try {
            // 1. 创建虚拟人配置
            VirtualHumanAttributes attributes = VirtualHumanAttributes.bottomCentered(
                1920, 1080, 600, 800);
            VirtualHuman virtualHuman = VirtualHuman.create(
                "80SdjjACwdZR73tRlZFYsg", "n0UcBeHCw3xK", 10, 600, 800, 660, 280);
            
            // 2. 创建TTS配置
            TTSConfig tts = new TTSConfig("181-hanfangziran")
                .withRate(1.1f)
                .withVolume(60);
            
            // 3. 创建PPT讲解文本（与PPT页面对应）
            List<String> voiceTexts = Arrays.asList(
                "欢迎大家参加今天的产品发布会",
                "首先让我们来看看产品的核心功能",
                "这个功能可以大大提升用户体验",
                "接下来是技术架构的介绍",
                "感谢大家的聆听，有任何问题欢迎提问"
            );
            
            // 4. 创建PPT视频请求
            PPTVideoRequest request = new PPTVideoRequest(
                "产品发布会PPT讲解",
                "https://example.com/presentation.pptx",
                virtualHuman,
                tts,
                voiceTexts
            );
            
            // 设置背景音乐
            request.withBackgroundMusic("https://example.com/soft-music.mp3", 0.2);
            
            // 设置字幕
            request.withCaption(50);
            
            // 5. 提交PPT视频生成任务
            BaseResponse<VideoTaskResponse> response = client.broadcasting()
                .createPPTVideo(request);
            
            if (response.isSuccess()) {
                String taskId = response.getData().getId();
                System.out.println("PPT视频生成任务已提交，任务ID: " + taskId);
                
                // 轮询查询任务状态
                pollTaskStatus(client, taskId);
            } else {
                System.err.println("PPT视频生成失败: " + response.getMessage());
            }
        } catch (Exception e) {
            System.err.println("PPT视频生成异常: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 创建透明数字人视频示例
     */
    public static void createTransparentVideoExample(AIBeingsClient client) {
        System.out.println("=== 创建透明数字人视频示例 ===");
        
        try {
            // 1. 创建虚拟人配置（居中显示）
            VirtualHumanAttributes attributes = VirtualHumanAttributes.centered(
                1920, 1080, 800, 1000);
            VirtualHuman virtualHuman = new VirtualHuman(
                "80SdjjACwdZR73tRlZFYsg", "n0UcBeHCw3xK", 10, attributes);
            
            // 2. 创建透明视频请求
            TransparentVideoRequest request = new TransparentVideoRequest(
                "透明数字人播报", virtualHuman);
            
            // 使用TTS驱动
            List<String> texts = Arrays.asList(
                "这是一个透明背景的数字人视频",
                "可以方便地与其他内容进行合成",
                "适用于各种场景的应用"
            );
            request.withTTS("181-hanfangziran", texts);
            
            // 设置视频尺寸
            request.withDimensions(1920, 1080);
            
            // 3. 提交透明视频生成任务
            BaseResponse<VideoTaskResponse> response = client.broadcasting()
                .createTransparentVideo(request);
            
            if (response.isSuccess()) {
                String taskId = response.getData().getId();
                System.out.println("透明视频生成任务已提交，任务ID: " + taskId);
                
                // 轮询查询任务状态
                pollTaskStatus(client, taskId);
            } else {
                System.err.println("透明视频生成失败: " + response.getMessage());
            }
        } catch (Exception e) {
            System.err.println("透明视频生成异常: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 生成TTS音频示例
     */
    public static void generateTTSAudioExample(AIBeingsClient client) {
        System.out.println("=== 生成TTS音频示例 ===");
        
        try {
            // 创建TTS音频请求
            TTSAudioRequest request = new TTSAudioRequest(
                "这是一段测试音频，用于演示TTS功能的效果。",
                "181-hanfangziran"
            );
            
            // 设置音频参数
            request.withRate(1.0f)
                   .withPitch(1.0f)
                   .withVolume(80)
                   .withFormat("mp3");
            
            // 生成音频
            BaseResponse<TTSAudioResponse> response = client.broadcasting()
                .generateTTSAudio(request);
            
            if (response.isSuccess()) {
                TTSAudioResponse audioResponse = response.getData();
                System.out.println("音频生成成功!");
                System.out.println("音频URL: " + audioResponse.getAudioUrl());
                System.out.println("音频时长: " + audioResponse.getDurationDescription());
                System.out.println("音频大小: " + String.format("%.2f MB", audioResponse.getAudioSizeMB()));
                System.out.println("音频格式: " + audioResponse.getFormat());
            } else {
                System.err.println("音频生成失败: " + response.getMessage());
            }
        } catch (Exception e) {
            System.err.println("音频生成异常: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 查询视频任务列表示例
     */
    public static void queryVideoTasksExample(AIBeingsClient client) {
        System.out.println("=== 查询视频任务列表示例 ===");
        
        try {
            // 创建查询请求
            VideoTaskQueryRequest request = new VideoTaskQueryRequest(1, 10);
            // 可以指定用户查询
            // request.withUserId("user123");
            
            // 查询任务列表
            BaseResponse<VideoTaskPageResponse> response = client.broadcasting()
                .queryVideoTasks(request);
            
            if (response.isSuccess()) {
                VideoTaskPageResponse pageResponse = response.getData();
                System.out.println("任务总数: " + pageResponse.getTotal());
                System.out.println("当前页: " + pageResponse.getCurrent() + "/" + pageResponse.getPages());
                System.out.println("本页任务数: " + pageResponse.getRecordCount());
                
                // 显示任务列表
                for (VideoTaskResult task : pageResponse.getRecords()) {
                    System.out.println("---");
                    System.out.println("任务ID: " + task.getId());
                    System.out.println("视频名称: " + task.getVideoName());
                    System.out.println("状态: " + task.getStatus());
                    System.out.println("进度: " + task.getProgressPercentage() + "%");
                    System.out.println("创建时间: " + task.getCreateTime());
                    
                    if (task.isFinished()) {
                        System.out.println("视频URL: " + task.getVideoUrl());
                        System.out.println("视频时长: " + task.getVideoDurationSeconds() + "秒");
                        System.out.println("视频大小: " + String.format("%.2f MB", task.getVideoSizeMB()));
                    } else if (task.isError()) {
                        System.out.println("错误信息: " + task.getError());
                    }
                }
            } else {
                System.err.println("查询任务列表失败: " + response.getMessage());
            }
        } catch (Exception e) {
            System.err.println("查询任务列表异常: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 轮询查询任务状态
     */
    private static void pollTaskStatus(AIBeingsClient client, String taskId) {
        System.out.println("开始轮询任务状态...");
        
        int maxAttempts = 20; // 最多查询20次
        int attempt = 0;
        
        while (attempt < maxAttempts) {
            try {
                BaseResponse<VideoTaskResult> response = client.broadcasting()
                    .getVideoTaskResult(taskId);
                
                if (response.isSuccess()) {
                    VideoTaskResult result = response.getData();
                    System.out.println("任务状态: " + result.getStatus() + 
                                     ", 进度: " + result.getProgressPercentage() + "%");
                    
                    if (result.isFinished()) {
                        System.out.println("任务完成!");
                        System.out.println("视频URL: " + result.getVideoUrl());
                        System.out.println("视频时长: " + result.getVideoDurationSeconds() + "秒");
                        System.out.println("视频大小: " + String.format("%.2f MB", result.getVideoSizeMB()));
                        break;
                    } else if (result.isError()) {
                        System.err.println("任务失败: " + result.getError());
                        break;
                    }
                } else {
                    System.err.println("查询任务状态失败: " + response.getMessage());
                    break;
                }
                
                // 等待30秒后再次查询
                Thread.sleep(30000);
                attempt++;
                
            } catch (InterruptedException e) {
                System.err.println("轮询被中断: " + e.getMessage());
                break;
            } catch (Exception e) {
                System.err.println("查询任务状态异常: " + e.getMessage());
                break;
            }
        }
        
        if (attempt >= maxAttempts) {
            System.out.println("轮询超时，请手动查询任务状态");
        }
    }
}