package com.lazhu.aibeings.model.account;

import com.lazhu.aibeings.model.BaseRequest;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 删除子账号请求
 */
@Data
@NoArgsConstructor
public class DeleteUserRequest extends BaseRequest {
    
    /**
     * 用户ID
     */
    private String userId;
    
    // 便捷构造函数
    public DeleteUserRequest(String userId) {
        this.userId = userId;
    }
    
    @Override
    public String toString() {
        return "DeleteUserRequest{" +
                "userId='" + userId + '\'' +
                '}';
    }
}