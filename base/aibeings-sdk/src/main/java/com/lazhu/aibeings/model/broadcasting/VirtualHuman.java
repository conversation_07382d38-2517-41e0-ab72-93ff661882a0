package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 虚拟人信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VirtualHuman {
    
    /**
     * 虚拟人ID
     */
    @JsonProperty("virtualHumanId")
    private String virtualHumanId;
    
    /**
     * 姿势ID
     */
    @JsonProperty("virtualHumanPostureId")
    private String virtualHumanPostureId;
    
    /**
     * 图层顺序
     */
    @JsonProperty("zIndex")
    private Integer zIndex;
    
    /**
     * 虚拟人属性
     */
    @JsonProperty("attributes")
    private VirtualHumanAttributes attributes;
    

    
    /**
     * 创建虚拟人配置
     */
    public static VirtualHuman create(String virtualHumanId, String postureId, int zIndex, 
                                    int width, int height, int x, int y) {
        VirtualHumanAttributes attributes = new VirtualHumanAttributes(width, height, x, y);
        return new VirtualHuman(virtualHumanId, postureId, zIndex, attributes);
    }
    
    /**
     * 验证虚拟人配置
     */
    public boolean isValid() {
        return virtualHumanPostureId != null && !virtualHumanPostureId.trim().isEmpty()
                && zIndex != null && zIndex > 1
                && attributes != null && attributes.isValid();
    }
}