package com.lazhu.aibeings.model.employee;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lazhu.aibeings.model.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 查询数字员工列表请求模型
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class DigitalEmployeeRequest extends BaseRequest {
    
    @JsonProperty("categoryList")
    private List<Integer> categoryList;
    
    @JsonProperty("keyword")
    private String keyword;
    
    @JsonProperty("modelType")
    private String modelType; // STUDIO：播报, INTERACTIVE：交互
    
    @JsonProperty("pageIndex")
    private Integer pageIndex;
    
    @JsonProperty("pageSize")
    private Integer pageSize;
    
    // 便捷构造函数
    public DigitalEmployeeRequest(String modelType, Integer pageIndex, Integer pageSize) {
        super();
        this.modelType = modelType;
        this.pageIndex = pageIndex;
        this.pageSize = pageSize;
    }
    
    /**
     * 数字员工分类枚举
     */
    public enum Category {
        OFFICIAL(0, "小冰官方精品员工"),
        ONLINE_CUSTOM(1, "在线定制员工"),
        OFFLINE_CUSTOM(2, "离线定制精品员工");
        
        private final int code;
        private final String description;
        
        Category(int code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public int getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 工作场景枚举
     */
    public enum ModelType {
        STUDIO("STUDIO", "播报"),
        INTERACTIVE("INTERACTIVE", "交互");
        
        private final String code;
        private final String description;
        
        ModelType(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
}