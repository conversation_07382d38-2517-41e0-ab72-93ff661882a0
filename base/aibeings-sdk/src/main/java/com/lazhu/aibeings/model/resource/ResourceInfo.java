package com.lazhu.aibeings.model.resource;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 企业资源信息模型
 */
@Data
@NoArgsConstructor
public class ResourceInfo {
    
    @JsonProperty("resourceCode")
    private String resourceCode;
    
    @JsonProperty("resourceName")
    private String resourceName;
    
    @JsonProperty("resourceUnit")
    private String resourceUnit;
    
    @JsonProperty("resourceType")
    private String resourceType; // rental: 租赁型, consume: 消耗型
    
    @JsonProperty("total")
    private Long total;
    
    @JsonProperty("used")
    private Long used;
    
    @JsonProperty("remaining")
    private Long remaining;
    
    @JsonProperty("expired")
    private Long expired;
    

    
    /**
     * 是否为租赁型资源
     */
    public boolean isRentalType() {
        return "rental".equals(resourceType);
    }
    
    /**
     * 是否为消耗型资源
     */
    public boolean isConsumeType() {
        return "consume".equals(resourceType);
    }
    
    /**
     * 获取使用率百分比
     */
    public double getUsagePercentage() {
        if (total == null || total == 0) {
            return 0.0;
        }
        return (used != null ? used.doubleValue() : 0.0) / total.doubleValue() * 100.0;
    }
    
    /**
     * 获取剩余率百分比
     */
    public double getRemainingPercentage() {
        if (total == null || total == 0) {
            return 0.0;
        }
        return (remaining != null ? remaining.doubleValue() : 0.0) / total.doubleValue() * 100.0;
    }
    
    /**
     * 获取可读的数量显示
     */
    public String getReadableTotal() {
        return formatQuantity(total) + " " + (resourceUnit != null ? resourceUnit : "");
    }
    
    public String getReadableUsed() {
        return formatQuantity(used) + " " + (resourceUnit != null ? resourceUnit : "");
    }
    
    public String getReadableRemaining() {
        return formatQuantity(remaining) + " " + (resourceUnit != null ? resourceUnit : "");
    }
    
    private String formatQuantity(Long quantity) {
        if (quantity == null) {
            return "0";
        }
        
        if (quantity >= 1_000_000_000) {
            return String.format("%.1fB", quantity / 1_000_000_000.0);
        } else if (quantity >= 1_000_000) {
            return String.format("%.1fM", quantity / 1_000_000.0);
        } else if (quantity >= 1_000) {
            return String.format("%.1fK", quantity / 1_000.0);
        } else {
            return quantity.toString();
        }
    }
}