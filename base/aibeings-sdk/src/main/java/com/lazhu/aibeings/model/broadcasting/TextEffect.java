package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文本特效
 */
@Data
@NoArgsConstructor
public class TextEffect {
    
    /**
     * 特效类型
     */
    @JsonProperty("type")
    private String type;
    
    /**
     * 特效持续时长（秒）
     */
    @JsonProperty("duration")
    private Double duration = 3.0;
    
    // 进入特效类型常量
    public static final String EFFECT_IN_BLUR = "blur_in";
    public static final String EFFECT_IN_SLIDE_RIGHT = "slide_right_in";
    public static final String EFFECT_IN_SLIDE_LEFT = "slide_left_in";
    public static final String EFFECT_IN_SUNRISE = "sunrise_in";
    public static final String EFFECT_IN_ZOOMOUT = "zoomout_in";
    public static final String EFFECT_IN_ZOOMIN = "zoomin_in";
    public static final String EFFECT_IN_SLIDE_DOWN = "slide_down_in";
    public static final String EFFECT_IN_SLIDE_UP = "slide_up_in";
    
    // 消失特效类型常量
    public static final String EFFECT_OUT_SLIDE_UP = "slide_up_out";
    public static final String EFFECT_OUT_SLIDE_DOWN = "slide_down_out";
    public static final String EFFECT_OUT_ZOOMIN = "zoomin_out";
    public static final String EFFECT_OUT_SUNSET = "sunset_out";
    public static final String EFFECT_OUT_SLIDE_LEFT = "slide_left_out";
    public static final String EFFECT_OUT_SLIDE_RIGHT = "slide_right_out";
    public static final String EFFECT_OUT_BLUR = "blur_out";
    
    // 便捷构造函数
    public TextEffect(String type, Double duration) {
        this.type = type;
        this.duration = duration;
    }
    
    /**
     * 创建进入特效
     */
    public static TextEffect fadeIn(double duration) {
        return new TextEffect(EFFECT_IN_BLUR, duration);
    }
    
    /**
     * 创建消失特效
     */
    public static TextEffect fadeOut(double duration) {
        return new TextEffect(EFFECT_OUT_BLUR, duration);
    }
    
    /**
     * 创建滑入特效
     */
    public static TextEffect slideIn(String direction, double duration) {
        String effectType;
        switch (direction.toLowerCase()) {
            case "left":
                effectType = EFFECT_IN_SLIDE_LEFT;
                break;
            case "right":
                effectType = EFFECT_IN_SLIDE_RIGHT;
                break;
            case "up":
                effectType = EFFECT_IN_SLIDE_UP;
                break;
            case "down":
                effectType = EFFECT_IN_SLIDE_DOWN;
                break;
            default:
                effectType = EFFECT_IN_SLIDE_LEFT;
        }
        return new TextEffect(effectType, duration);
    }
    
    /**
     * 创建滑出特效
     */
    public static TextEffect slideOut(String direction, double duration) {
        String effectType;
        switch (direction.toLowerCase()) {
            case "left":
                effectType = EFFECT_OUT_SLIDE_LEFT;
                break;
            case "right":
                effectType = EFFECT_OUT_SLIDE_RIGHT;
                break;
            case "up":
                effectType = EFFECT_OUT_SLIDE_UP;
                break;
            case "down":
                effectType = EFFECT_OUT_SLIDE_DOWN;
                break;
            default:
                effectType = EFFECT_OUT_SLIDE_LEFT;
        }
        return new TextEffect(effectType, duration);
    }
}