package com.lazhu.aibeings.model.customize;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 抠像预览视频DTO
 */
@Data
@NoArgsConstructor
public class PreviewVideoDTO {
    
    /**
     * 背景图片地址
     */
    private String backgroundImage;
    
    /**
     * 背景颜色，白，粉，黑
     */
    private String colorName;
    
    /**
     * 5秒webm透明通道视频地址，用于预览抠像效果
     */
    private String webmVideo;
    
    /**
     * 背景图和webm叠加合成的预览视频地址，用于预览抠像效果
     */
    private String resultUrl;
    
    // 便捷构造函数
    public PreviewVideoDTO(String backgroundImage, String colorName, String webmVideo, String resultUrl) {
        this.backgroundImage = backgroundImage;
        this.colorName = colorName;
        this.webmVideo = webmVideo;
        this.resultUrl = resultUrl;
    }
    
    /**
     * 背景颜色枚举
     */
    public enum BackgroundColor {
        WHITE("白色"),
        PINK("粉色"),
        BLACK("黑色");
        
        private final String name;
        
        BackgroundColor(String name) {
            this.name = name;
        }
        
        public String getName() {
            return name;
        }
    }
    
    @Override
    public String toString() {
        return "PreviewVideoDTO{" +
                "backgroundImage='" + backgroundImage + '\'' +
                ", colorName='" + colorName + '\'' +
                ", webmVideo='" + webmVideo + '\'' +
                ", resultUrl='" + resultUrl + '\'' +
                '}';
    }
}