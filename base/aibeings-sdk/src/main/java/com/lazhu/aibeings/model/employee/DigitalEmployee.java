package com.lazhu.aibeings.model.employee;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数字员工信息模型
 */
@Data
@NoArgsConstructor
public class DigitalEmployee {
    
    @JsonProperty("bizId")
    private String bizId;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("summaryImage")
    private String summaryImage;
    
    @JsonProperty("summaryVideo")
    private String summaryVideo;
    
    @JsonProperty("projectVideo")
    private String projectVideo;
    
    @JsonProperty("industry")
    private String industry;
    
    @JsonProperty("language")
    private String language;
    
    @JsonProperty("introduce")
    private String introduce;
    
    @JsonProperty("experience")
    private String experience;
    
    @JsonProperty("category")
    private Integer category;
    

    
    /**
     * 获取分类描述
     */
    public String getCategoryDescription() {
        if (category == null) {
            return "未知";
        }
        
        switch (category) {
            case 0:
                return "小冰官方精品员工";
            case 1:
                return "在线定制员工";
            case 2:
                return "离线定制精品员工";
            default:
                return "未知分类";
        }
    }
}