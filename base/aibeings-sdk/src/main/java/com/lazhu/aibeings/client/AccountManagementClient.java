package com.lazhu.aibeings.client;

import com.lazhu.aibeings.config.AIBeingsConfig;
import com.lazhu.aibeings.model.BaseResponse;
import com.lazhu.aibeings.model.account.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 账号管理客户端
 * 
 * 提供子账号的增删改查功能，包括：
 * - 查询子账号列表
 * - 添加子账号
 * - 编辑子账号
 * - 删除子账号
 * - 查询单个子账号
 */
public class AccountManagementClient {
    
    private static final Logger logger = LoggerFactory.getLogger(AccountManagementClient.class);
    
    private final HttpClient httpClient;
    
    public AccountManagementClient(AIBeingsConfig config) {
        // 使用正确的基础URL
        AIBeingsConfig accountConfig = new AIBeingsConfig(config.getSubscriptionKey());
        accountConfig.setBaseUrl("https://openapi.xiaoice.com/vh");
        accountConfig.setConnectTimeout(config.getConnectTimeout());
        accountConfig.setReadTimeout(config.getReadTimeout());
        accountConfig.setMaxRetries(config.getMaxRetries());
        
        this.httpClient = new HttpClient(accountConfig);
    }
    
    /**
     * 查询子账号列表
     * 
     * @param request 查询请求
     * @return 子账号列表响应
     */
    public BaseResponse<UserQueryResponse> queryUsers(UserQueryRequest request) {
        logger.debug("Querying users with pageIndex: {}, pageSize: {}, orgId: {}", 
                    request.getPageIndex(), request.getPageSize(), request.getOrgId());
        
        return httpClient.post("/openapi/user/query-page", request, UserQueryResponse.class);
    }
    
    /**
     * 查询子账号列表 - 简化版本
     * 
     * @param pageIndex 页码（从1开始）
     * @param pageSize 每页显示的记录数量
     * @return 子账号列表响应
     */
    public BaseResponse<UserQueryResponse> queryUsers(int pageIndex, int pageSize) {
        UserQueryRequest request = new UserQueryRequest(pageIndex, pageSize);
        return queryUsers(request);
    }
    
    /**
     * 查询指定组织的子账号列表
     * 
     * @param pageIndex 页码（从1开始）
     * @param pageSize 每页显示的记录数量
     * @param orgId 组织ID
     * @return 子账号列表响应
     */
    public BaseResponse<UserQueryResponse> queryUsers(int pageIndex, int pageSize, int orgId) {
        UserQueryRequest request = new UserQueryRequest(pageIndex, pageSize, orgId);
        return queryUsers(request);
    }
    
    /**
     * 添加子账号
     * 
     * @param request 添加用户请求
     * @return 添加结果，data字段为用户ID
     */
    public BaseResponse<String> addUser(AddUserRequest request) {
        logger.debug("Adding user with displayName: {}, email: {}, role: {}, orgId: {}", 
                    request.getDisplayName(), request.getEmail(), request.getRole(), request.getOrgId());
        
        return httpClient.post("/openapi/user/add-user/v2", request, String.class);
    }
    
    /**
     * 添加子账号 - 简化版本
     * 
     * @param displayName 用户名称
     * @param email 用户邮箱
     * @param password 用户密码
     * @param role 用户角色 (admin/user)
     * @return 添加结果，data字段为用户ID
     */
    public BaseResponse<String> addUser(String displayName, String email, String password, String role) {
        AddUserRequest request = new AddUserRequest(displayName, email, password, role);
        return addUser(request);
    }
    
    /**
     * 添加管理员账号
     * 
     * @param displayName 用户名称
     * @param email 用户邮箱
     * @param password 用户密码
     * @return 添加结果，data字段为用户ID
     */
    public BaseResponse<String> addAdmin(String displayName, String email, String password) {
        return addUser(displayName, email, password, AddUserRequest.Role.ADMIN.getCode());
    }
    
    /**
     * 添加普通用户账号
     * 
     * @param displayName 用户名称
     * @param email 用户邮箱
     * @param password 用户密码
     * @return 添加结果，data字段为用户ID
     */
    public BaseResponse<String> addNormalUser(String displayName, String email, String password) {
        return addUser(displayName, email, password, AddUserRequest.Role.USER.getCode());
    }
    
    /**
     * 编辑子账号
     * 
     * @param request 更新用户请求
     * @return 更新结果，data字段为操作是否成功
     */
    public BaseResponse<Boolean> updateUser(UpdateUserRequest request) {
        logger.debug("Updating user with id: {}, displayName: {}, email: {}, role: {}, lock: {}", 
                    request.getId(), request.getDisplayName(), request.getEmail(), 
                    request.getRole(), request.getLock());
        
        return httpClient.post("/openapi/user/update/v2", request, Boolean.class);
    }
    
    /**
     * 更新用户基本信息
     * 
     * @param userId 用户ID
     * @param displayName 新的用户名称
     * @param email 新的用户邮箱
     * @return 更新结果
     */
    public BaseResponse<Boolean> updateUserInfo(String userId, String displayName, String email) {
        UpdateUserRequest request = new UpdateUserRequest(userId);
        request.setDisplayName(displayName);
        request.setEmail(email);
        return updateUser(request);
    }
    
    /**
     * 更新用户角色
     * 
     * @param userId 用户ID
     * @param role 新角色 (admin/user)
     * @return 更新结果
     */
    public BaseResponse<Boolean> updateUserRole(String userId, String role) {
        UpdateUserRequest request = new UpdateUserRequest(userId);
        request.setRole(role);
        return updateUser(request);
    }
    
    /**
     * 更新用户密码
     * 
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 更新结果
     */
    public BaseResponse<Boolean> updateUserPassword(String userId, String oldPassword, String newPassword) {
        UpdateUserRequest request = new UpdateUserRequest(userId);
        request.setOldPassword(oldPassword);
        request.setPassword(newPassword);
        return updateUser(request);
    }
    
    /**
     * 锁定用户账号
     * 
     * @param userId 用户ID
     * @return 更新结果
     */
    public BaseResponse<Boolean> lockUser(String userId) {
        UpdateUserRequest request = new UpdateUserRequest(userId);
        request.setLock(true);
        return updateUser(request);
    }
    
    /**
     * 解锁用户账号
     * 
     * @param userId 用户ID
     * @return 更新结果
     */
    public BaseResponse<Boolean> unlockUser(String userId) {
        UpdateUserRequest request = new UpdateUserRequest(userId);
        request.setLock(false);
        return updateUser(request);
    }
    
    /**
     * 删除子账号
     * 
     * @param request 删除用户请求
     * @return 删除结果，data字段为操作是否成功
     */
    public BaseResponse<Boolean> deleteUser(DeleteUserRequest request) {
        logger.debug("Deleting user with userId: {}", request.getUserId());
        
        return httpClient.post("/openapi/user/delete", request, Boolean.class);
    }
    
    /**
     * 删除子账号 - 简化版本
     * 
     * @param userId 用户ID
     * @return 删除结果，data字段为操作是否成功
     */
    public BaseResponse<Boolean> deleteUser(String userId) {
        DeleteUserRequest request = new DeleteUserRequest(userId);
        return deleteUser(request);
    }
    
    /**
     * 查询单个子账号
     * 
     * @param request 查询用户请求
     * @return 用户信息
     */
    public BaseResponse<UserVO> getUser(GetUserRequest request) {
        logger.debug("Getting user with userId: {}, userEmail: {}, userPhone: {}", 
                    request.getUserId(), request.getUserEmail(), request.getUserPhone());
        
        return httpClient.post("/openapi/user/get/v2", request, UserVO.class);
    }
    
    /**
     * 根据用户ID查询单个子账号
     * 
     * @param userId 用户ID
     * @return 用户信息
     */
    public BaseResponse<UserVO> getUserById(String userId) {
        GetUserRequest request = GetUserRequest.byUserId(userId);
        return getUser(request);
    }
    
    /**
     * 根据用户邮箱查询单个子账号
     * 
     * @param userEmail 用户邮箱
     * @return 用户信息
     */
    public BaseResponse<UserVO> getUserByEmail(String userEmail) {
        GetUserRequest request = GetUserRequest.byUserEmail(userEmail);
        return getUser(request);
    }
    
    /**
     * 根据用户手机号查询单个子账号
     * 
     * @param userPhone 用户手机号
     * @return 用户信息
     */
    public BaseResponse<UserVO> getUserByPhone(String userPhone) {
        GetUserRequest request = GetUserRequest.byUserPhone(userPhone);
        return getUser(request);
    }
}