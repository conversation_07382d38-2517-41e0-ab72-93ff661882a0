package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * 数字员工详情信息
 */
@Data
@NoArgsConstructor
public class DigitalEmployeeDetail {
    
    /**
     * 数字员工ID
     */
    @JsonProperty("virtualHumanId")
    private String virtualHumanId;
    
    /**
     * 默认绑定的声音ID
     */
    @JsonProperty("defaultVoiceId")
    private String defaultVoiceId;
    
    /**
     * 数字员工类型
     * 0：小冰官方精品员工
     * 1：企业定制
     */
    @JsonProperty("category")
    private Integer category;
    
    /**
     * 是否支持生成透明虚拟人视频
     */
    @JsonProperty("supportTransparency")
    private Boolean supportTransparency;
    
    /**
     * 支持的声音列表
     */
    @JsonProperty("voiceInfos")
    private List<VoiceInfo> voiceInfos;
    
    /**
     * 支持的动作列表
     */
    @JsonProperty("postureInfos")
    private List<PostureInfo> postureInfos;
    

    
    /**
     * 获取分类描述
     */
    public String getCategoryDescription() {
        if (category == null) return "未知";
        switch (category) {
            case 0: return "小冰官方精品员工";
            case 1: return "企业定制";
            default: return "未知";
        }
    }
    
    /**
     * 是否为官方员工
     */
    public boolean isOfficialEmployee() {
        return category != null && category == 0;
    }
    
    /**
     * 是否为企业定制员工
     */
    public boolean isCustomEmployee() {
        return category != null && category == 1;
    }
}