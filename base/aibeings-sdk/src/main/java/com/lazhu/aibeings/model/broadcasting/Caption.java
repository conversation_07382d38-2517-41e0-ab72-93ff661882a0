package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 字幕设置
 */
@Data
@NoArgsConstructor
public class Caption {
    
    /**
     * 范围内居右
     */
    @JsonProperty("topRight")
    private Boolean topRight = false;
    
    /**
     * 范围内居左
     */
    @JsonProperty("topLeft")
    private Boolean topLeft = false;
    
    /**
     * 范围内居中
     */
    @JsonProperty("topCenter")
    private Boolean topCenter = false;
    
    /**
     * 字幕图层
     */
    @JsonProperty("zIndex")
    private Integer zIndex;
    
    /**
     * 字幕属性
     */
    @JsonProperty("attributes")
    private DisplayTextAttributes attributes;
    
    // 便捷构造函数
    public Caption(Integer zIndex) {
        this.zIndex = zIndex;
        this.topCenter = true; // 默认居中
    }
    
    public Caption(Integer zIndex, DisplayTextAttributes attributes) {
        this.zIndex = zIndex;
        this.attributes = attributes;
        this.topCenter = true; // 默认居中
    }
    
    /**
     * 设置居中对齐
     */
    public Caption center() {
        this.topLeft = false;
        this.topRight = false;
        this.topCenter = true;
        return this;
    }
    
    /**
     * 设置左对齐
     */
    public Caption left() {
        this.topLeft = true;
        this.topRight = false;
        this.topCenter = false;
        return this;
    }
    
    /**
     * 设置右对齐
     */
    public Caption right() {
        this.topLeft = false;
        this.topRight = true;
        this.topCenter = false;
        return this;
    }
    
    /**
     * 设置字幕属性
     */
    public Caption withAttributes(DisplayTextAttributes attributes) {
        this.attributes = attributes;
        return this;
    }
    
    /**
     * 创建默认字幕配置
     */
    public static Caption defaultCaption(int zIndex) {
        Caption caption = new Caption(zIndex);
        
        // 设置默认属性
        DisplayTextAttributes attributes = new DisplayTextAttributes();
        attributes.setFont(6); // 微软雅黑
        attributes.setFontSize(40);
        attributes.setFontColor("#ffffff");
        attributes.setSpacing(0);
        
        caption.setAttributes(attributes);
        return caption;
    }
    
    /**
     * 验证字幕配置
     */
    public boolean isValid() {
        return zIndex != null && zIndex >= 1 && zIndex <= 100;
    }
}