package com.lazhu.aibeings.model.subscription;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 订阅查询响应数据模型
 */
@Data
@NoArgsConstructor
public class SubscriptionResponse {
    
    @JsonProperty("records")
    private List<SubscriptionInfo> records;
    
    @JsonProperty("total")
    private Long total;
    
    @JsonProperty("size")
    private Integer size;
    
    @JsonProperty("current")
    private Integer current;
    
    @JsonProperty("pages")
    private Integer pages;
    
    @JsonProperty("orders")
    private List<Object> orders;
    
    @JsonProperty("hitCount")
    private Boolean hitCount;
    
    @JsonProperty("searchCount")
    private Boolean searchCount;
    

    
    /**
     * 是否还有下一页
     */
    public boolean hasNext() {
        return current != null && pages != null && current < pages;
    }
    
    /**
     * 是否有上一页
     */
    public boolean hasPrevious() {
        return current != null && current > 1;
    }
    
    /**
     * 获取有效订阅列表
     */
    public List<SubscriptionInfo> getValidSubscriptions() {
        if (records == null) {
            return java.util.Collections.emptyList();
        }
        
        return records.stream()
            .filter(SubscriptionInfo::isValid)
            .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 获取已过期订阅列表
     */
    public List<SubscriptionInfo> getExpiredSubscriptions() {
        if (records == null) {
            return java.util.Collections.emptyList();
        }
        
        return records.stream()
            .filter(SubscriptionInfo::isExpired)
            .collect(java.util.stream.Collectors.toList());
    }
}