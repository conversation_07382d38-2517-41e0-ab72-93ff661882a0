package com.lazhu.aibeings.client;

import com.lazhu.aibeings.config.AIBeingsConfig;
import com.lazhu.aibeings.model.BaseResponse;
import com.lazhu.aibeings.model.PageRequest;
import com.lazhu.aibeings.model.employee.DigitalEmployee;
import com.lazhu.aibeings.model.employee.DigitalEmployeeRequest;
import com.lazhu.aibeings.model.employee.DigitalEmployeeResponse;
import com.lazhu.aibeings.model.resource.ResourceInfo;
import com.lazhu.aibeings.model.subscription.SubscriptionInfo;
import com.lazhu.aibeings.model.subscription.SubscriptionResponse;
import com.lazhu.aibeings.model.voice.VoiceInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 基础数据管理客户端
 */
public class DataManagementClient {
    
    private static final Logger logger = LoggerFactory.getLogger(DataManagementClient.class);
    
    private final HttpClient httpClient;
    
    public DataManagementClient(AIBeingsConfig config) {
        // 更新配置以使用正确的基础URL
        AIBeingsConfig dataConfig = new AIBeingsConfig(config.getSubscriptionKey());
        dataConfig.setBaseUrl("https://openapi.xiaoice.com/vh");
        dataConfig.setConnectTimeout(config.getConnectTimeout());
        dataConfig.setReadTimeout(config.getReadTimeout());
        dataConfig.setMaxRetries(config.getMaxRetries());
        
        this.httpClient = new HttpClient(dataConfig);
    }
    
    /**
     * 查询企业数字员工列表
     * 
     * @param request 查询请求
     * @return 数字员工列表响应
     */
    public BaseResponse<DigitalEmployeeResponse> queryDigitalEmployees(DigitalEmployeeRequest request) {
        logger.debug("Querying digital employees with modelType: {}, pageIndex: {}, pageSize: {}", 
                    request.getModelType(), request.getPageIndex(), request.getPageSize());
        
        return httpClient.post("/openapi/video/queryDigitalEmployee", request, DigitalEmployeeResponse.class);
    }
    
    /**
     * 查询数字员工列表 - 简化版本
     * 
     * @param modelType 工作场景 (STUDIO/INTERACTIVE)
     * @param pageIndex 页码 (从1开始)
     * @param pageSize 页大小 (最大200)
     * @return 数字员工列表响应
     */
    public BaseResponse<DigitalEmployeeResponse> queryDigitalEmployees(String modelType, int pageIndex, int pageSize) {
        DigitalEmployeeRequest request = new DigitalEmployeeRequest(modelType, pageIndex, pageSize);
        return queryDigitalEmployees(request);
    }
    
    /**
     * 查询播报场景的数字员工
     * 
     * @param pageIndex 页码
     * @param pageSize 页大小
     * @return 数字员工列表响应
     */
    public BaseResponse<DigitalEmployeeResponse> queryStudioEmployees(int pageIndex, int pageSize) {
        return queryDigitalEmployees(DigitalEmployeeRequest.ModelType.STUDIO.getCode(), pageIndex, pageSize);
    }
    
    /**
     * 查询交互场景的数字员工
     * 
     * @param pageIndex 页码
     * @param pageSize 页大小
     * @return 数字员工列表响应
     */
    public BaseResponse<DigitalEmployeeResponse> queryInteractiveEmployees(int pageIndex, int pageSize) {
        return queryDigitalEmployees(DigitalEmployeeRequest.ModelType.INTERACTIVE.getCode(), pageIndex, pageSize);
    }
    
    /**
     * 根据关键字搜索数字员工
     * 
     * @param keyword 搜索关键字
     * @param modelType 工作场景
     * @param pageIndex 页码
     * @param pageSize 页大小
     * @return 数字员工列表响应
     */
    public BaseResponse<DigitalEmployeeResponse> searchDigitalEmployees(String keyword, String modelType, 
                                                                       int pageIndex, int pageSize) {
        DigitalEmployeeRequest request = new DigitalEmployeeRequest(modelType, pageIndex, pageSize);
        request.setKeyword(keyword);
        return queryDigitalEmployees(request);
    }
    
    /**
     * 根据分类查询数字员工
     * 
     * @param categories 分类列表
     * @param modelType 工作场景
     * @param pageIndex 页码
     * @param pageSize 页大小
     * @return 数字员工列表响应
     */
    public BaseResponse<DigitalEmployeeResponse> queryDigitalEmployeesByCategory(List<Integer> categories, 
                                                                                String modelType, 
                                                                                int pageIndex, int pageSize) {
        DigitalEmployeeRequest request = new DigitalEmployeeRequest(modelType, pageIndex, pageSize);
        request.setCategoryList(categories);
        return queryDigitalEmployees(request);
    }
    
    /**
     * 查询官方精品员工
     * 
     * @param modelType 工作场景
     * @param pageIndex 页码
     * @param pageSize 页大小
     * @return 数字员工列表响应
     */
    public BaseResponse<DigitalEmployeeResponse> queryOfficialEmployees(String modelType, int pageIndex, int pageSize) {
        return queryDigitalEmployeesByCategory(
            Arrays.asList(DigitalEmployeeRequest.Category.OFFICIAL.getCode()), 
            modelType, pageIndex, pageSize
        );
    }
    
    /**
     * 查询企业声音列表
     * 
     * @return 声音列表响应
     */
    public BaseResponse<List<VoiceInfo>> queryVoiceList() {
        logger.debug("Querying enterprise voice list");
        
        // 使用泛型安全的方式处理
        Map<String, Object> params = new HashMap<>();
        BaseResponse<?> response = httpClient.get("/openapi/customize/zero/voice-list", params, Object.class);
        
        // 手动转换响应数据
        if (response.isSuccess() && response.getData() != null) {
            try {
                @SuppressWarnings("unchecked")
                List<Object> rawList = (List<Object>) response.getData();
                List<VoiceInfo> voiceList = new ArrayList<>();
                
                // 这里需要根据实际API响应格式进行转换
                // 为了编译通过，暂时返回空列表，实际使用时需要根据API响应格式实现转换逻辑
                return new BaseResponse<>(response.getCode(), response.getMessage(), voiceList);
            } catch (Exception e) {
                logger.warn("Failed to convert voice list response: {}", e.getMessage());
                return new BaseResponse<>(500, "数据转换失败", null);
            }
        }
        
        return new BaseResponse<>(response.getCode(), response.getMessage(), null);
    }
    
    /**
     * 根据语言筛选声音列表
     * 
     * @param language 语言代码 (如: zh-CN, en-US)
     * @return 筛选后的声音列表
     */
    public List<VoiceInfo> filterVoicesByLanguage(String language) {
        BaseResponse<List<VoiceInfo>> response = queryVoiceList();
        if (response.isSuccess() && response.getData() != null) {
            return response.getData().stream()
                .filter(voice -> language.equals(voice.getLanguage()))
                .collect(java.util.stream.Collectors.toList());
        }
        return java.util.Collections.emptyList();
    }
    
    /**
     * 获取支持交互的声音列表
     * 
     * @return 支持交互的声音列表
     */
    public List<VoiceInfo> getInteractiveSupportedVoices() {
        BaseResponse<List<VoiceInfo>> response = queryVoiceList();
        if (response.isSuccess() && response.getData() != null) {
            return response.getData().stream()
                .filter(VoiceInfo::isInteractiveSupported)
                .collect(java.util.stream.Collectors.toList());
        }
        return java.util.Collections.emptyList();
    }
    
    /**
     * 查询企业权益数据
     * 
     * @return 企业权益数据列表
     */
    public BaseResponse<List<ResourceInfo>> queryEnterpriseResources() {
        logger.debug("Querying enterprise resources");
        
        // 使用泛型安全的方式处理
        Map<String, Object> params = new HashMap<>();
        BaseResponse<?> response = httpClient.get("/openapi/res/query", params, Object.class);
        
        // 手动转换响应数据
        if (response.isSuccess() && response.getData() != null) {
            try {
                @SuppressWarnings("unchecked")
                List<Object> rawList = (List<Object>) response.getData();
                List<ResourceInfo> resourceList = new ArrayList<>();
                
                // 这里需要根据实际API响应格式进行转换
                // 为了编译通过，暂时返回空列表，实际使用时需要根据API响应格式实现转换逻辑
                return new BaseResponse<>(response.getCode(), response.getMessage(), resourceList);
            } catch (Exception e) {
                logger.warn("Failed to convert resource list response: {}", e.getMessage());
                return new BaseResponse<>(500, "数据转换失败", null);
            }
        }
        
        return new BaseResponse<>(response.getCode(), response.getMessage(), null);
    }
    
    /**
     * 查询企业订购License记录
     * 
     * @param pageIndex 页索引 (从1开始)
     * @param pageSize 页大小 (最大100)
     * @return License订购记录响应
     */
    public BaseResponse<SubscriptionResponse> queryLicenseSubscriptions(int pageIndex, int pageSize) {
        logger.debug("Querying license subscriptions with pageIndex: {}, pageSize: {}", pageIndex, pageSize);
        
        PageRequest request = new PageRequest(pageIndex, pageSize);
        return httpClient.post("/openapi/res/query-lic-subs", request, SubscriptionResponse.class);
    }
    
    /**
     * 查询企业订购资源包记录
     * 
     * @param pageIndex 页索引 (从1开始)
     * @param pageSize 页大小 (最大100)
     * @return 资源包订购记录响应
     */
    public BaseResponse<SubscriptionResponse> queryPackageSubscriptions(int pageIndex, int pageSize) {
        logger.debug("Querying package subscriptions with pageIndex: {}, pageSize: {}", pageIndex, pageSize);
        
        PageRequest request = new PageRequest(pageIndex, pageSize);
        return httpClient.post("/openapi/res/query-pkg-subs", request, SubscriptionResponse.class);
    }
    
    /**
     * 获取所有有效的订阅记录
     * 
     * @return 有效订阅列表
     */
    public List<SubscriptionInfo> getValidSubscriptions() {
        try {
            BaseResponse<SubscriptionResponse> licenseResponse = queryLicenseSubscriptions(1, 100);
            BaseResponse<SubscriptionResponse> packageResponse = queryPackageSubscriptions(1, 100);
            
            List<SubscriptionInfo> validSubscriptions = new java.util.ArrayList<>();
            
            if (licenseResponse.isSuccess() && licenseResponse.getData() != null) {
                validSubscriptions.addAll(licenseResponse.getData().getValidSubscriptions());
            }
            
            if (packageResponse.isSuccess() && packageResponse.getData() != null) {
                validSubscriptions.addAll(packageResponse.getData().getValidSubscriptions());
            }
            
            return validSubscriptions;
            
        } catch (Exception e) {
            logger.error("Failed to get valid subscriptions: {}", e.getMessage(), e);
            return java.util.Collections.emptyList();
        }
    }
    
    /**
     * 根据资源类型筛选企业资源
     * 
     * @param resourceType 资源类型 ("rental" 或 "consume")
     * @return 筛选后的资源列表
     */
    public List<ResourceInfo> filterResourcesByType(String resourceType) {
        BaseResponse<List<ResourceInfo>> response = queryEnterpriseResources();
        if (response.isSuccess() && response.getData() != null) {
            return response.getData().stream()
                .filter(resource -> resourceType.equals(resource.getResourceType()))
                .collect(java.util.stream.Collectors.toList());
        }
        return java.util.Collections.emptyList();
    }
    
    /**
     * 获取租赁型资源列表
     * 
     * @return 租赁型资源列表
     */
    public List<ResourceInfo> getRentalResources() {
        return filterResourcesByType("rental");
    }
    
    /**
     * 获取消耗型资源列表
     * 
     * @return 消耗型资源列表
     */
    public List<ResourceInfo> getConsumeResources() {
        return filterResourcesByType("consume");
    }
}