package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * TTS配置
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TTSConfig {
    
    /**
     * 声音ID
     */
    @JsonProperty("voiceId")
    private String voiceId;
    
    /**
     * 语速，默认1，取值范围 >=0.6, <=1.5
     */
    @JsonProperty("rate")
    private Float rate = 1.0f;
    
    /**
     * 语调，默认1，取值范围 >=0.6, <=1.5
     */
    @JsonProperty("pitch")
    private Float pitch = 1.0f;
    
    /**
     * 音量，默认50，取值范围 >=0 <=100
     */
    @JsonProperty("volume")
    private Integer volume = 50;
    
    // 便捷构造函数
    public TTSConfig(String voiceId) {
        this.voiceId = voiceId;
    }
    
    /**
     * 设置语速
     */
    public TTSConfig withRate(float rate) {
        if (rate >= 0.6f && rate <= 1.5f) {
            this.rate = rate;
        }
        return this;
    }
    
    /**
     * 设置语调
     */
    public TTSConfig withPitch(float pitch) {
        if (pitch >= 0.6f && pitch <= 1.5f) {
            this.pitch = pitch;
        }
        return this;
    }
    
    /**
     * 设置音量
     */
    public TTSConfig withVolume(int volume) {
        if (volume >= 0 && volume <= 100) {
            this.volume = volume;
        }
        return this;
    }
    
    /**
     * 验证TTS配置
     */
    public boolean isValid() {
        if (voiceId == null || voiceId.trim().isEmpty()) {
            return false;
        }
        if (rate != null && (rate < 0.6f || rate > 1.5f)) {
            return false;
        }
        if (pitch != null && (pitch < 0.6f || pitch > 1.5f)) {
            return false;
        }
        if (volume != null && (volume < 0 || volume > 100)) {
            return false;
        }
        return true;
    }
}