package com.lazhu.aibeings.model.customize;

import com.lazhu.aibeings.model.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 声音入驻/取消入驻请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class VoiceCheckInRequest extends BaseRequest {
    
    /**
     * 任务id，在创建声音训练任务时的返回结果中获取
     */
    private Integer taskId;
    
    /**
     * true:入驻, false: 取消入驻
     */
    private Boolean checkIn;
    
    /**
     * 声音名称
     */
    private String voiceName;
    
    // 便捷构造函数
    public VoiceCheckInRequest(Integer taskId, Boolean checkIn) {
        this.taskId = taskId;
        this.checkIn = checkIn;
    }
    
    public VoiceCheckInRequest(Integer taskId, Boolean checkIn, String voiceName) {
        this.taskId = taskId;
        this.checkIn = checkIn;
        this.voiceName = voiceName;
    }
}