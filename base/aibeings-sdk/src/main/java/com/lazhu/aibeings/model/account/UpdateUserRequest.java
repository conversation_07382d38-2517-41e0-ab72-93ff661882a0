package com.lazhu.aibeings.model.account;

import com.lazhu.aibeings.model.BaseRequest;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 编辑子账号请求
 */
@Data
@NoArgsConstructor
public class UpdateUserRequest extends BaseRequest {
    
    /**
     * 用户ID (必填)
     */
    private String id;
    
    /**
     * 所属组织ID，未开通组织不用填写
     */
    private Integer orgId;
    
    /**
     * 用户名称
     */
    private String displayName;
    
    /**
     * 用户邮箱
     */
    private String email;
    
    /**
     * 用户新密码
     */
    private String password;
    
    /**
     * 用户旧密码，修改密码时需填入
     */
    private String oldPassword;
    
    /**
     * 用户新角色
     */
    private String role;
    
    /**
     * 是否锁定账号 (true、false)
     */
    private Boolean lock;
    
    // 便捷构造函数
    public UpdateUserRequest(String id) {
        this.id = id;
    }
    
    /**
     * 用户角色枚举
     */
    public enum Role {
        ADMIN("admin", "管理员"),
        USER("user", "普通用户");
        
        private final String code;
        private final String description;
        
        Role(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    @Override
    public String toString() {
        return "UpdateUserRequest{" +
                "id='" + id + '\'' +
                ", orgId=" + orgId +
                ", displayName='" + displayName + '\'' +
                ", email='" + email + '\'' +
                ", password='[PROTECTED]'" +
                ", oldPassword='[PROTECTED]'" +
                ", role='" + role + '\'' +
                ", lock=" + lock +
                '}';
    }
}