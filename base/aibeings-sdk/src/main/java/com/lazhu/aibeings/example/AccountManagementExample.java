package com.lazhu.aibeings.example;

import com.lazhu.aibeings.AIBeingsClient;
import com.lazhu.aibeings.client.AccountManagementClient;
import com.lazhu.aibeings.model.BaseResponse;
import com.lazhu.aibeings.model.account.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 账号管理示例
 * 
 * 演示如何使用AccountManagementClient进行子账号的增删改查操作
 */
public class AccountManagementExample {
    
    private static final Logger logger = LoggerFactory.getLogger(AccountManagementExample.class);
    
    public static void main(String[] args) {
        // 替换为您的实际subscription key
        String subscriptionKey = "b4403d4d90e34c0d9c728917a61d3e4b";
        
        // 创建客户端
        AIBeingsClient client = new AIBeingsClient(subscriptionKey);
        AccountManagementClient accountClient = client.accountManagement();
        
        try {
            // 示例1: 查询子账号列表
            queryUsersExample(accountClient);
            
//             示例2: 添加子账号
             String newUserId = addUserExample(accountClient);
            
             // 示例3: 查询单个子账号
             if (newUserId != null) {
                 getUserExample(accountClient, newUserId);
                
                 // 示例4: 编辑子账号
                 updateUserExample(accountClient, newUserId);
                
                 // 示例5: 删除子账号
                 deleteUserExample(accountClient, newUserId);
             }
            
        } catch (Exception e) {
            logger.error("Account management example failed: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 查询子账号列表示例
     */
    private static void queryUsersExample(AccountManagementClient accountClient) {
        logger.info("=== 查询子账号列表示例 ===");
        
        try {
            // 查询第一页，每页10条记录
            BaseResponse<UserQueryResponse> response = accountClient.queryUsers(1, 10);
            
            if (response.isSuccess()) {
                UserQueryResponse data = response.getData();
                logger.info("查询成功，总记录数: {}, 当前页: {}, 每页大小: {}", 
                           data.getTotal(), data.getPageIndex(), data.getPageSize());
                
                if (data.getList() != null && !data.getList().isEmpty()) {
                    logger.info("用户列表:");
                    for (UserVO user : data.getList()) {
                        logger.info("- ID: {}, 姓名: {}, 邮箱: {}, 角色: {}, 状态: {}", 
                                   user.getId(), user.getDisplayName(), user.getEmail(), 
                                   user.getRole(), user.isEnabled() ? "启用" : "禁用");
                    }
                } else {
                    logger.info("暂无用户数据");
                }
            } else {
                logger.error("查询失败: {} - {}", response.getCode(), response.getMessage());
            }
            
        } catch (Exception e) {
            logger.error("查询子账号列表失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 添加子账号示例
     */
    private static String addUserExample(AccountManagementClient accountClient) {
        logger.info("=== 添加子账号示例 ===");
        
        try {
            // 添加一个普通用户
            BaseResponse<String> response = accountClient.addNormalUser(
                "测试用户", 
                "<EMAIL>", 
                "TestPassword123"
            );
            
            if (response.isSuccess()) {
                String userId = response.getData();
                logger.info("添加用户成功，用户ID: {}", userId);
                return userId;
            } else {
                logger.error("添加用户失败: {} - {}", response.getCode(), response.getMessage());
                return null;
            }
            
        } catch (Exception e) {
            logger.error("添加子账号失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 查询单个子账号示例
     */
    private static void getUserExample(AccountManagementClient accountClient, String userId) {
        logger.info("=== 查询单个子账号示例 ===");
        
        try {
            // 根据用户ID查询
            BaseResponse<UserVO> response = accountClient.getUserById(userId);
            
            if (response.isSuccess()) {
                UserVO user = response.getData();
                logger.info("查询用户成功:");
                logger.info("- ID: {}", user.getId());
                logger.info("- 姓名: {}", user.getDisplayName());
                logger.info("- 邮箱: {}", user.getEmail());
                logger.info("- 组织ID: {}", user.getOrgId());
                logger.info("- 角色: {}", user.getRole());
                logger.info("- 状态: {}", user.isEnabled() ? "启用" : "禁用");
                logger.info("- 是否管理员: {}", user.isAdmin());
            } else {
                logger.error("查询用户失败: {} - {}", response.getCode(), response.getMessage());
            }
            
        } catch (Exception e) {
            logger.error("查询单个子账号失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 编辑子账号示例
     */
    private static void updateUserExample(AccountManagementClient accountClient, String userId) {
        logger.info("=== 编辑子账号示例 ===");
        
        try {
            // 更新用户基本信息
            BaseResponse<Boolean> response1 = accountClient.updateUserInfo(
                userId, 
                "更新后的用户名", 
                "<EMAIL>"
            );
            
            if (response1.isSuccess() && response1.getData()) {
                logger.info("更新用户基本信息成功");
            } else {
                logger.error("更新用户基本信息失败: {} - {}", response1.getCode(), response1.getMessage());
            }
            
            // 更新用户角色为管理员
            BaseResponse<Boolean> response2 = accountClient.updateUserRole(
                userId, 
                UpdateUserRequest.Role.ADMIN.getCode()
            );
            
            if (response2.isSuccess() && response2.getData()) {
                logger.info("更新用户角色成功");
            } else {
                logger.error("更新用户角色失败: {} - {}", response2.getCode(), response2.getMessage());
            }
            
            // 锁定用户账号
            BaseResponse<Boolean> response3 = accountClient.lockUser(userId);
            
            if (response3.isSuccess() && response3.getData()) {
                logger.info("锁定用户账号成功");
            } else {
                logger.error("锁定用户账号失败: {} - {}", response3.getCode(), response3.getMessage());
            }
            
            // 解锁用户账号
            BaseResponse<Boolean> response4 = accountClient.unlockUser(userId);
            
            if (response4.isSuccess() && response4.getData()) {
                logger.info("解锁用户账号成功");
            } else {
                logger.error("解锁用户账号失败: {} - {}", response4.getCode(), response4.getMessage());
            }
            
        } catch (Exception e) {
            logger.error("编辑子账号失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 删除子账号示例
     */
    private static void deleteUserExample(AccountManagementClient accountClient, String userId) {
        logger.info("=== 删除子账号示例 ===");
        
        try {
            BaseResponse<Boolean> response = accountClient.deleteUser(userId);
            
            if (response.isSuccess() && response.getData()) {
                logger.info("删除用户成功，用户ID: {}", userId);
            } else {
                logger.error("删除用户失败: {} - {}", response.getCode(), response.getMessage());
            }
            
        } catch (Exception e) {
            logger.error("删除子账号失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 高级查询示例
     */
    public static void advancedQueryExample(AccountManagementClient accountClient) {
        logger.info("=== 高级查询示例 ===");
        
        try {
            // 查询指定组织的用户
            int orgId = 240;
            BaseResponse<UserQueryResponse> response = accountClient.queryUsers(1, 20, orgId);
            
            if (response.isSuccess()) {
                UserQueryResponse data = response.getData();
                logger.info("组织 {} 的用户数量: {}", orgId, data.getTotal());
                
                // 统计管理员和普通用户数量
                long adminCount = data.getList().stream().filter(UserVO::isAdmin).count();
                long userCount = data.getList().stream().filter(UserVO::isUser).count();
                long lockedCount = data.getList().stream().filter(UserVO::isLocked).count();
                
                logger.info("管理员数量: {}, 普通用户数量: {}, 被锁定用户数量: {}", 
                           adminCount, userCount, lockedCount);
            }
            
            // 根据邮箱查询用户
            BaseResponse<UserVO> emailResponse = accountClient.getUserByEmail("<EMAIL>");
            if (emailResponse.isSuccess()) {
                UserVO user = emailResponse.getData();
                logger.info("根据邮箱查询到用户: {}", user.getDisplayName());
            }
            
        } catch (Exception e) {
            logger.error("高级查询失败: {}", e.getMessage(), e);
        }
    }
}