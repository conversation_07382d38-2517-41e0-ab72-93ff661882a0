package com.lazhu.aibeings.model.account;

import com.lazhu.aibeings.model.BaseRequest;

/**
 * 添加子账号请求
 */
public class AddUserRequest extends BaseRequest {
    
    /**
     * 用户名称
     */
    private String displayName;
    
    /**
     * 用户邮箱
     */
    private String email;
    
    /**
     * 用户密码 (8-30位大小写字母/数字/字符组合)
     */
    private String password;
    
    /**
     * 用户角色 (admin=管理员 user=普通用户)
     */
    private String role;
    
    /**
     * 组织ID，用户所属的组织，未开通组织不用填写
     */
    private Integer orgId;
    
    public AddUserRequest() {}
    
    public AddUserRequest(String displayName, String email, String password, String role) {
        this.displayName = displayName;
        this.email = email;
        this.password = password;
        this.role = role;
    }
    
    public AddUserRequest(String displayName, String email, String password, String role, Integer orgId) {
        this.displayName = displayName;
        this.email = email;
        this.password = password;
        this.role = role;
        this.orgId = orgId;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getRole() {
        return role;
    }
    
    public void setRole(String role) {
        this.role = role;
    }
    
    public Integer getOrgId() {
        return orgId;
    }
    
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }
    
    /**
     * 用户角色枚举
     */
    public enum Role {
        ADMIN("admin", "管理员"),
        USER("user", "普通用户");
        
        private final String code;
        private final String description;
        
        Role(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    @Override
    public String toString() {
        return "AddUserRequest{" +
                "displayName='" + displayName + '\'' +
                ", email='" + email + '\'' +
                ", password='[PROTECTED]'" +
                ", role='" + role + '\'' +
                ", orgId=" + orgId +
                '}';
    }
}