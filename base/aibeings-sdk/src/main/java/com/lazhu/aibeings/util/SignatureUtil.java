package com.lazhu.aibeings.util;

import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import cn.hutool.core.util.StrUtil;
import java.util.Map;
import java.util.TreeMap;

/**
 * Signature utility for AI Beings API authentication
 */
public class SignatureUtil {
    
    /**
     * Generate signature for API request
     * 
     * @param params Request parameters
     * @param secretKey Secret key
     * @return Generated signature
     */
    public static String generateSignature(Map<String, Object> params, String secretKey) {
        if (params == null || params.isEmpty() || StrUtil.isBlank(secretKey)) {
            throw new IllegalArgumentException("Parameters and secret key cannot be empty");
        }
        
        // Sort parameters by key
        TreeMap<String, Object> sortedParams = new TreeMap<>(params);
        
        // Build query string
        StringBuilder queryString = new StringBuilder();
        for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
            if (entry.getValue() != null) {
                if (queryString.length() > 0) {
                    queryString.append("&");
                }
                queryString.append(entry.getKey()).append("=").append(entry.getValue());
            }
        }
        
        // Generate HMAC-SHA256 signature
        HMac hmac = new HMac(HmacAlgorithm.HmacSHA256, secretKey.getBytes());
        return hmac.digestHex(queryString.toString());
    }
    
    /**
     * Verify signature
     * 
     * @param params Request parameters
     * @param signature Signature to verify
     * @param secretKey Secret key
     * @return true if signature is valid
     */
    public static boolean verifySignature(Map<String, Object> params, String signature, String secretKey) {
        if (StrUtil.isBlank(signature)) {
            return false;
        }
        
        try {
            String expectedSignature = generateSignature(params, secretKey);
            return signature.equals(expectedSignature);
        } catch (Exception e) {
            return false;
        }
    }
}