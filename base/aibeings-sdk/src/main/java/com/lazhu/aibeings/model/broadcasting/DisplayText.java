package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 显示文本配置
 */
@Data
@NoArgsConstructor
public class DisplayText {
    
    /**
     * 文本内容
     */
    @JsonProperty("content")
    private String content;
    
    /**
     * 花字模板名称
     */
    @JsonProperty("artFontTemplateName")
    private String artFontTemplateName;
    
    /**
     * 图层号
     */
    @JsonProperty("zIndex")
    private Integer zIndex;
    
    /**
     * 文本属性
     */
    @JsonProperty("attributes")
    private DisplayTextAttributes attributes;
    
    /**
     * 进入特效
     */
    @JsonProperty("effectIn")
    private TextEffect effectIn;
    
    /**
     * 消失特效
     */
    @JsonProperty("effectOut")
    private TextEffect effectOut;
    
    // 便捷构造函数
    public DisplayText(String content, Integer zIndex, DisplayTextAttributes attributes) {
        this.content = content;
        this.zIndex = zIndex;
        this.attributes = attributes;
    }
    
    /**
     * 设置花字模板
     */
    public DisplayText withArtFontTemplate(String templateName) {
        this.artFontTemplateName = templateName;
        return this;
    }
    
    /**
     * 设置进入特效
     */
    public DisplayText withEffectIn(String type, Double duration) {
        this.effectIn = new TextEffect(type, duration);
        return this;
    }
    
    /**
     * 设置消失特效
     */
    public DisplayText withEffectOut(String type, Double duration) {
        this.effectOut = new TextEffect(type, duration);
        return this;
    }
    
    /**
     * 验证显示文本配置
     */
    public boolean isValid() {
        return content != null && !content.trim().isEmpty()
                && zIndex != null && zIndex >= 1 && zIndex <= 100
                && attributes != null && attributes.isValid();
    }
}