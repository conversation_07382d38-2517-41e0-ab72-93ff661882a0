package com.lazhu.aibeings.model.broadcasting;

import com.lazhu.aibeings.model.BaseRequest;
import java.util.List;

/**
 * 删除视频任务请求
 */
public class DeleteVideoTasksRequest extends BaseRequest {
    
    /**
     * 任务ID列表
     */
    private List<String> taskIds;
    
    public DeleteVideoTasksRequest() {
        super();
    }
    
    public DeleteVideoTasksRequest(List<String> taskIds) {
        super();
        this.taskIds = taskIds;
    }
    
    public List<String> getTaskIds() {
        return taskIds;
    }
    
    public void setTaskIds(List<String> taskIds) {
        this.taskIds = taskIds;
    }
}