package com.lazhu.aibeings.model.customize;

import java.util.List;

import com.lazhu.aibeings.model.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 创建声音训练任务请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class VoiceTrainingTaskRequest extends BaseRequest {
    
    /**
     * 音频训练数据列表
     */
    private List<AudioTrainingData> trainingData;
    
    // 便捷构造函数
    public VoiceTrainingTaskRequest(List<AudioTrainingData> trainingData) {
        this.trainingData = trainingData;
    }
    
    @Override
    public String toString() {
        return "VoiceTrainingTaskRequest{" +
                "trainingData=" + trainingData +
                '}';
    }
}