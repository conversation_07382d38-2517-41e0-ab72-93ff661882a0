package com.lazhu.aibeings.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Base response model for AI Beings API
 */
@Data
@NoArgsConstructor
public class BaseResponse<T> {
    
    @JsonProperty("code")
    private Integer code;
    
    @JsonProperty("message")
    private String message;
    
    @JsonProperty("data")
    private T data;
    
    
    @JsonProperty("traceId")
    private String traceId;
    
    // 便捷构造函数
    public BaseResponse(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }
    
    // 业务方法
    public boolean isSuccess() {
        return code != null && code == 200;
    }
}