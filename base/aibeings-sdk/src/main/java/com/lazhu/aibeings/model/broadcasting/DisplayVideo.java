package com.lazhu.aibeings.model.broadcasting;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 显示视频配置
 */
public class DisplayVideo {
    
    /**
     * 视频URL
     */
    @JsonProperty("mediaUrl")
    private String mediaUrl;
    
    /**
     * 图层顺序
     */
    @JsonProperty("zIndex")
    private Integer zIndex;
    
    /**
     * 视频属性
     */
    @JsonProperty("attributes")
    private DisplayVideoAttributes attributes;
    
    // Constructors
    public DisplayVideo() {}
    
    public DisplayVideo(String mediaUrl, Integer zIndex, DisplayVideoAttributes attributes) {
        this.mediaUrl = mediaUrl;
        this.zIndex = zIndex;
        this.attributes = attributes;
    }
    
    // Getters and Setters
    public String getMediaUrl() {
        return mediaUrl;
    }
    
    public void setMediaUrl(String mediaUrl) {
        this.mediaUrl = mediaUrl;
    }
    
    public Integer getZIndex() {
        return zIndex;
    }
    
    public void setZIndex(Integer zIndex) {
        this.zIndex = zIndex;
    }
    
    public DisplayVideoAttributes getAttributes() {
        return attributes;
    }
    
    public void setAttributes(DisplayVideoAttributes attributes) {
        this.attributes = attributes;
    }
    
    /**
     * 验证显示视频配置
     */
    public boolean isValid() {
        return mediaUrl != null && !mediaUrl.trim().isEmpty()
                && zIndex != null && zIndex >= 1 && zIndex <= 100
                && attributes != null && attributes.isValid();
    }
}