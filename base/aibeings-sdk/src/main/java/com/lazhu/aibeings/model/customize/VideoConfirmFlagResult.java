package com.lazhu.aibeings.model.customize;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 视频确认标志结果
 */
@Data
@NoArgsConstructor
public class VideoConfirmFlagResult {
    
    /**
     * 任务id
     */
    private String taskId;
    
    /**
     * true 通过预览结果，继续训练
     * false 拒绝预览结果，任务失败，返还定制权益
     */
    private Boolean confirmFlag;
    
    // 便捷构造函数
    public VideoConfirmFlagResult(String taskId, Boolean confirmFlag) {
        this.taskId = taskId;
        this.confirmFlag = confirmFlag;
    }
}