package com.lazhu.aibeings.model.customize;

import com.lazhu.aibeings.model.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 确认抠像效果请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class MattingConfirmRequest extends BaseRequest {
    
    /**
     * 任务id，在创建形象训练任务时的返回结果中获取
     */
    private String taskId;
    
    /**
     * true 通过预览结果，继续训练
     * false 拒绝预览结果，任务失败，返还定制权益
     */
    private Boolean confirmFlag;
    
    // 便捷构造函数
    public MattingConfirmRequest(String taskId, Boolean confirmFlag) {
        this.taskId = taskId;
        this.confirmFlag = confirmFlag;
    }
    
    @Override
    public String toString() {
        return "MattingConfirmRequest{" +
                "taskId='" + taskId + '\'' +
                ", confirmFlag=" + confirmFlag +
                '}';
    }
}