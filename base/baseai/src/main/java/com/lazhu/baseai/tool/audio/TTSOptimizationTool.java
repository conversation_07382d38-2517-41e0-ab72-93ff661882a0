package com.lazhu.baseai.tool.audio;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * TTS优化工具类
 * 提供语音合成优化、音频质量提升、性能优化等功能
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-01
 */
@Slf4j
public class TTSOptimizationTool {

    private static final String DEFAULT_OUTPUT_DIR = "tts_optimized";
    private static final int DEFAULT_THREAD_POOL_SIZE = 4;
    private static final long DEFAULT_TIMEOUT_SECONDS = 300;
    
    private final ExecutorService executorService;
    private final TTSOptimizationConfig config;
    
    public TTSOptimizationTool() {
        this(new TTSOptimizationConfig());
    }
    
    public TTSOptimizationTool(TTSOptimizationConfig config) {
        this.config = config;
        this.executorService = Executors.newFixedThreadPool(
            config.getThreadPoolSize() != null ? config.getThreadPoolSize() : DEFAULT_THREAD_POOL_SIZE
        );
    }

    /**
     * 优化单个文本的TTS生成
     * 
     * @param request 优化请求
     * @return 优化结果
     */
    public TTSOptimizationResponse optimizeSingleText(TTSOptimizationRequest request) {
        log.info("开始优化单个文本TTS: {}", request.getText());
        
        try {
            // 1. 文本预处理
            String optimizedText = preprocessText(request.getText());
            
            // 2. 选择最优音色
            String optimalVoiceId = selectOptimalVoice(request);
            
            // 3. 生成音频
            CosyVoiceTool.AudioInfo audioInfo = generateOptimizedAudio(
                optimizedText, optimalVoiceId, request);
            
            // 4. 音频后处理
            String processedAudioPath = postProcessAudio(audioInfo, request);
            
            // 5. 质量评估
            AudioQualityMetrics metrics = assessAudioQuality(processedAudioPath);
            
            return TTSOptimizationResponse.builder()
                .success(true)
                .originalText(request.getText())
                .optimizedText(optimizedText)
                .voiceId(optimalVoiceId)
                .audioPath(processedAudioPath)
                .duration(audioInfo.timeLong())
                .qualityMetrics(metrics)
                .processingTime(System.currentTimeMillis() - request.getStartTime())
                .build();
                
        } catch (Exception e) {
            log.error("TTS优化失败: {}", e.getMessage(), e);
            return TTSOptimizationResponse.builder()
                .success(false)
                .errorMessage(e.getMessage())
                .build();
        }
    }

    /**
     * 批量优化TTS生成
     * 
     * @param requests 批量请求列表
     * @return 批量优化结果
     */
    public List<TTSOptimizationResponse> optimizeBatch(List<TTSOptimizationRequest> requests) {
        log.info("开始批量TTS优化，共{}个任务", requests.size());
        
        List<CompletableFuture<TTSOptimizationResponse>> futures = new ArrayList<>();
        
        for (TTSOptimizationRequest request : requests) {
            CompletableFuture<TTSOptimizationResponse> future = CompletableFuture
                .supplyAsync(() -> optimizeSingleText(request), executorService);
            futures.add(future);
        }
        
        // 等待所有任务完成
        List<TTSOptimizationResponse> results = new ArrayList<>();
        for (CompletableFuture<TTSOptimizationResponse> future : futures) {
            try {
                TTSOptimizationResponse response = future.get(DEFAULT_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                results.add(response);
            } catch (Exception e) {
                log.error("批量处理任务失败: {}", e.getMessage(), e);
                results.add(TTSOptimizationResponse.builder()
                    .success(false)
                    .errorMessage("任务超时或执行失败: " + e.getMessage())
                    .build());
            }
        }
        
        log.info("批量TTS优化完成，成功{}个，失败{}个", 
            results.stream().mapToInt(r -> r.isSuccess() ? 1 : 0).sum(),
            results.stream().mapToInt(r -> r.isSuccess() ? 0 : 1).sum());
        
        return results;
    }

    /**
     * 文本预处理 - 优化文本以提高TTS质量
     */
    private String preprocessText(String text) {
        if (StrUtil.isBlank(text)) {
            return text;
        }
        
        String processed = text;
        
        // 1. 标点符号优化
        processed = optimizePunctuation(processed);
        
        // 2. 数字和特殊符号处理
        processed = normalizeNumbersAndSymbols(processed);
        
        // 3. 语音停顿优化
        processed = optimizePauses(processed);
        
        // 4. 长句分割
        processed = splitLongSentences(processed);
        
        log.debug("文本预处理完成: {} -> {}", text, processed);
        return processed;
    }

    /**
     * 选择最优音色
     */
    private String selectOptimalVoice(TTSOptimizationRequest request) {
        // 如果指定了音色，直接使用
        if (StrUtil.isNotBlank(request.getVoiceId())) {
            return request.getVoiceId();
        }
        
        // 根据文本内容和场景选择最优音色
        String text = request.getText();
        String scene = request.getScene();
        
        // 根据场景选择音色
        if ("formal".equals(scene)) {
            return config.getFormalVoiceId();
        } else if ("casual".equals(scene)) {
            return config.getCasualVoiceId();
        } else if ("emotional".equals(scene)) {
            return config.getEmotionalVoiceId();
        }
        
        // 默认音色
        return config.getDefaultVoiceId();
    }

    /**
     * 生成优化的音频
     */
    private CosyVoiceTool.AudioInfo generateOptimizedAudio(String text, String voiceId, 
            TTSOptimizationRequest request) {
        
        String outputDir = request.getOutputDir() != null ? request.getOutputDir() : DEFAULT_OUTPUT_DIR;
        String fileName = generateFileName(text, voiceId, outputDir);
        
        // 确保输出目录存在
        FileUtil.mkdirsSafely(new File(outputDir), 1, 100);
        
        return CosyVoiceTool.createAudio(request.getSequence(), voiceId, text, fileName);
    }

    /**
     * 音频后处理
     */
    private String postProcessAudio(CosyVoiceTool.AudioInfo audioInfo, TTSOptimizationRequest request) {
        // 这里可以添加音频后处理逻辑，如音量调整、降噪等
        // 目前直接返回原始路径
        return generateFileName(audioInfo.txt(), request.getVoiceId(), 
            request.getOutputDir() != null ? request.getOutputDir() : DEFAULT_OUTPUT_DIR);
    }

    /**
     * 评估音频质量
     */
    private AudioQualityMetrics assessAudioQuality(String audioPath) {
        File audioFile = new File(audioPath);
        if (!audioFile.exists()) {
            return AudioQualityMetrics.builder()
                .qualityScore(0.0)
                .build();
        }
        
        // 基础质量指标
        long fileSize = audioFile.length();
        long duration = Mp3DurationReader.getMillDuration(audioFile);
        
        // 计算质量分数（简化版本）
        double qualityScore = calculateQualityScore(fileSize, duration);
        
        return AudioQualityMetrics.builder()
            .qualityScore(qualityScore)
            .fileSize(fileSize)
            .duration(duration)
            .bitrate(calculateBitrate(fileSize, duration))
            .build();
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String text, String voiceId, String outputDir) {
        String hash = String.valueOf(Math.abs((text + voiceId).hashCode()));
        return outputDir + File.separator + "tts_" + hash + ".mp3";
    }

    /**
     * 计算质量分数
     */
    private double calculateQualityScore(long fileSize, long duration) {
        if (duration <= 0) return 0.0;
        
        // 基于文件大小和时长的简单质量评估
        double bitrate = (fileSize * 8.0) / (duration / 1000.0);
        
        // 标准化到0-1范围
        if (bitrate >= 256000) return 1.0;
        if (bitrate <= 64000) return 0.3;
        
        return 0.3 + (bitrate - 64000) / (256000 - 64000) * 0.7;
    }

    /**
     * 计算比特率
     */
    private long calculateBitrate(long fileSize, long duration) {
        if (duration <= 0) return 0;
        return (fileSize * 8 * 1000) / duration;
    }

    /**
     * 标点符号优化
     */
    private String optimizePunctuation(String text) {
        return text
            .replaceAll("([。！？])([^\\s])", "$1 $2")  // 句号后加空格
            .replaceAll("([，；])([^\\s])", "$1 $2")    // 逗号分号后加空格
            .replaceAll("\\s+", " ")                    // 多个空格合并为一个
            .trim();
    }

    /**
     * 数字和特殊符号处理
     */
    private String normalizeNumbersAndSymbols(String text) {
        return text
            .replaceAll("\\d+", this::convertNumberToWords)  // 数字转文字
            .replaceAll("&", "和")                           // 符号转换
            .replaceAll("%", "百分之")
            .replaceAll("@", "at");
    }

    /**
     * 数字转文字（简化版本）
     */
    private String convertNumberToWords(String number) {
        // 这里可以实现更复杂的数字转文字逻辑
        // 目前返回原数字
        return number;
    }

    /**
     * 语音停顿优化
     */
    private String optimizePauses(String text) {
        return text
            .replaceAll("([。！？])", "$1，")  // 句末添加停顿
            .replaceAll("([，；])", "$1 ");   // 逗号后添加短停顿
    }

    /**
     * 长句分割
     */
    private String splitLongSentences(String text) {
        if (text.length() <= config.getMaxSentenceLength()) {
            return text;
        }

        // 按标点符号分割长句
        String[] sentences = text.split("[。！？]");
        StringBuilder result = new StringBuilder();

        for (String sentence : sentences) {
            if (sentence.trim().length() > 0) {
                if (sentence.length() > config.getMaxSentenceLength()) {
                    // 进一步分割
                    String[] parts = sentence.split("[，；]");
                    for (int i = 0; i < parts.length; i++) {
                        result.append(parts[i].trim());
                        if (i < parts.length - 1) {
                            result.append("，");
                        }
                    }
                } else {
                    result.append(sentence.trim());
                }
                result.append("。");
            }
        }

        return result.toString();
    }

    /**
     * 获取优化统计信息
     */
    public TTSOptimizationStats getOptimizationStats() {
        return TTSOptimizationStats.builder()
            .totalProcessed(0L)  // 这里可以添加统计逻辑
            .successCount(0L)
            .failureCount(0L)
            .averageProcessingTime(0.0)
            .averageQualityScore(0.0)
            .build();
    }

    /**
     * 清理临时文件
     */
    public void cleanupTempFiles(String outputDir) {
        try {
            File dir = new File(outputDir);
            if (dir.exists() && dir.isDirectory()) {
                File[] files = dir.listFiles((file, name) -> name.startsWith("temp_"));
                if (files != null) {
                    for (File file : files) {
                        if (file.delete()) {
                            log.debug("删除临时文件: {}", file.getName());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("清理临时文件失败: {}", e.getMessage());
        }
    }

    /**
     * 关闭资源
     */
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
