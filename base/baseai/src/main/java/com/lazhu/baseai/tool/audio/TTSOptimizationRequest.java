package com.lazhu.baseai.tool.audio;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Map;

/**
 * TTS优化请求类
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TTSOptimizationRequest {
    
    /**
     * 请求ID
     */
    private String requestId;
    
    /**
     * 要转换的文本
     */
    private String text;
    
    /**
     * 指定的音色ID（可选）
     */
    private String voiceId;
    
    /**
     * 场景类型：formal(正式), casual(休闲), emotional(情感)
     */
    private String scene;
    
    /**
     * 序列号（用于批量处理）
     */
    @Builder.Default
    private Integer sequence = 0;
    
    /**
     * 输出目录
     */
    private String outputDir;
    
    /**
     * 文件名前缀
     */
    private String filePrefix;
    
    /**
     * 优先级 (1-10, 10最高)
     */
    @Builder.Default
    private Integer priority = 5;
    
    /**
     * 是否启用文本预处理
     */
    @Builder.Default
    private Boolean enablePreprocessing = true;
    
    /**
     * 是否启用音频后处理
     */
    @Builder.Default
    private Boolean enablePostProcessing = true;
    
    /**
     * 是否启用质量评估
     */
    @Builder.Default
    private Boolean enableQualityAssessment = true;
    
    /**
     * 语音速度 (0.5-2.0)
     */
    @Builder.Default
    private Double speechRate = 1.0;
    
    /**
     * 音量 (0-100)
     */
    @Builder.Default
    private Integer volume = 80;
    
    /**
     * 音调 (-20 到 +20)
     */
    @Builder.Default
    private Integer pitch = 0;
    
    /**
     * 音频格式
     */
    @Builder.Default
    private String audioFormat = "MP3";
    
    /**
     * 采样率
     */
    @Builder.Default
    private Integer sampleRate = 44100;
    
    /**
     * 比特率
     */
    @Builder.Default
    private Integer bitrate = 256;
    
    /**
     * 情感类型：neutral(中性), happy(快乐), sad(悲伤), angry(愤怒), excited(兴奋)
     */
    @Builder.Default
    private String emotion = "neutral";
    
    /**
     * 情感强度 (0.0-1.0)
     */
    @Builder.Default
    private Double emotionIntensity = 0.5;
    
    /**
     * 语言代码
     */
    @Builder.Default
    private String language = "zh-CN";
    
    /**
     * 自定义参数
     */
    private Map<String, Object> customParams;
    
    /**
     * 回调URL（异步处理时使用）
     */
    private String callbackUrl;
    
    /**
     * 请求开始时间
     */
    @Builder.Default
    private Long startTime = System.currentTimeMillis();
    
    /**
     * 超时时间（秒）
     */
    @Builder.Default
    private Integer timeoutSeconds = 300;
    
    /**
     * 重试次数
     */
    @Builder.Default
    private Integer retryCount = 0;
    
    /**
     * 最大重试次数
     */
    @Builder.Default
    private Integer maxRetries = 3;
    
    /**
     * 用户ID（用于统计和限流）
     */
    private String userId;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 创建简单请求
     */
    public static TTSOptimizationRequest createSimple(String text) {
        return TTSOptimizationRequest.builder()
            .text(text)
            .requestId(generateRequestId())
            .build();
    }
    
    /**
     * 创建带音色的请求
     */
    public static TTSOptimizationRequest createWithVoice(String text, String voiceId) {
        return TTSOptimizationRequest.builder()
            .text(text)
            .voiceId(voiceId)
            .requestId(generateRequestId())
            .build();
    }
    
    /**
     * 创建场景化请求
     */
    public static TTSOptimizationRequest createWithScene(String text, String scene) {
        return TTSOptimizationRequest.builder()
            .text(text)
            .scene(scene)
            .requestId(generateRequestId())
            .build();
    }
    
    /**
     * 创建高质量请求
     */
    public static TTSOptimizationRequest createHighQuality(String text) {
        return TTSOptimizationRequest.builder()
            .text(text)
            .bitrate(320)
            .sampleRate(48000)
            .enablePreprocessing(true)
            .enablePostProcessing(true)
            .enableQualityAssessment(true)
            .requestId(generateRequestId())
            .build();
    }
    
    /**
     * 创建快速请求
     */
    public static TTSOptimizationRequest createFast(String text) {
        return TTSOptimizationRequest.builder()
            .text(text)
            .bitrate(128)
            .enablePreprocessing(false)
            .enablePostProcessing(false)
            .enableQualityAssessment(false)
            .timeoutSeconds(60)
            .requestId(generateRequestId())
            .build();
    }
    
    /**
     * 生成请求ID
     */
    private static String generateRequestId() {
        return "tts_" + System.currentTimeMillis() + "_" + 
               String.valueOf(Math.abs(Math.random() * 10000)).substring(0, 4);
    }
    
    /**
     * 验证请求有效性
     */
    public boolean isValid() {
        return text != null && !text.trim().isEmpty()
            && speechRate != null && speechRate >= 0.5 && speechRate <= 2.0
            && volume != null && volume >= 0 && volume <= 100
            && pitch != null && pitch >= -20 && pitch <= 20
            && emotionIntensity != null && emotionIntensity >= 0.0 && emotionIntensity <= 1.0
            && priority != null && priority >= 1 && priority <= 10;
    }
    
    /**
     * 获取请求摘要
     */
    public String getRequestSummary() {
        return String.format("TTS请求[ID:%s, 文本长度:%d, 音色:%s, 场景:%s]", 
            requestId, text != null ? text.length() : 0, voiceId, scene);
    }
    
    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount = (this.retryCount == null ? 0 : this.retryCount) + 1;
    }
    
    /**
     * 是否可以重试
     */
    public boolean canRetry() {
        return retryCount != null && maxRetries != null && retryCount < maxRetries;
    }
    
    /**
     * 获取处理时长
     */
    public long getProcessingDuration() {
        return System.currentTimeMillis() - (startTime != null ? startTime : 0);
    }
}
