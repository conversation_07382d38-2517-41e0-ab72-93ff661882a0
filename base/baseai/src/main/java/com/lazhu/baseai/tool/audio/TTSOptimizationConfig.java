package com.lazhu.baseai.tool.audio;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * TTS优化配置类
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TTSOptimizationConfig {
    
    /**
     * 默认音色ID
     */
    @Builder.Default
    private String defaultVoiceId = "longxiaochun_v2";
    
    /**
     * 正式场景音色ID
     */
    @Builder.Default
    private String formalVoiceId = "longxiaochun_v2";
    
    /**
     * 休闲场景音色ID
     */
    @Builder.Default
    private String casualVoiceId = "longxiaochun_v2";
    
    /**
     * 情感场景音色ID
     */
    @Builder.Default
    private String emotionalVoiceId = "longxiaochun_v2";
    
    /**
     * 线程池大小
     */
    @Builder.Default
    private Integer threadPoolSize = 4;
    
    /**
     * 最大句子长度
     */
    @Builder.Default
    private Integer maxSentenceLength = 100;
    
    /**
     * 音频质量等级 (1-5)
     */
    @Builder.Default
    private Integer qualityLevel = 3;
    
    /**
     * 是否启用音频后处理
     */
    @Builder.Default
    private Boolean enablePostProcessing = true;
    
    /**
     * 是否启用质量评估
     */
    @Builder.Default
    private Boolean enableQualityAssessment = true;
    
    /**
     * 输出目录
     */
    @Builder.Default
    private String outputDirectory = "tts_output";
    
    /**
     * 临时文件目录
     */
    @Builder.Default
    private String tempDirectory = "tts_temp";
    
    /**
     * 最大重试次数
     */
    @Builder.Default
    private Integer maxRetries = 3;
    
    /**
     * 请求超时时间（秒）
     */
    @Builder.Default
    private Integer timeoutSeconds = 300;
    
    /**
     * 是否启用缓存
     */
    @Builder.Default
    private Boolean enableCache = true;
    
    /**
     * 缓存过期时间（小时）
     */
    @Builder.Default
    private Integer cacheExpirationHours = 24;
    
    /**
     * 音频格式
     */
    @Builder.Default
    private String audioFormat = "MP3";
    
    /**
     * 采样率
     */
    @Builder.Default
    private Integer sampleRate = 44100;
    
    /**
     * 比特率
     */
    @Builder.Default
    private Integer bitrate = 256;
    
    /**
     * 是否启用文本预处理
     */
    @Builder.Default
    private Boolean enableTextPreprocessing = true;
    
    /**
     * 是否启用智能音色选择
     */
    @Builder.Default
    private Boolean enableSmartVoiceSelection = true;
    
    /**
     * 语音速度调整 (0.5-2.0)
     */
    @Builder.Default
    private Double speechRate = 1.0;
    
    /**
     * 音量调整 (0-100)
     */
    @Builder.Default
    private Integer volume = 80;
    
    /**
     * 音调调整 (-20 到 +20)
     */
    @Builder.Default
    private Integer pitch = 0;
    
    /**
     * 创建默认配置
     */
    public static TTSOptimizationConfig createDefault() {
        return TTSOptimizationConfig.builder().build();
    }
    
    /**
     * 创建高质量配置
     */
    public static TTSOptimizationConfig createHighQuality() {
        return TTSOptimizationConfig.builder()
            .qualityLevel(5)
            .bitrate(320)
            .enablePostProcessing(true)
            .enableQualityAssessment(true)
            .enableTextPreprocessing(true)
            .enableSmartVoiceSelection(true)
            .build();
    }
    
    /**
     * 创建快速配置
     */
    public static TTSOptimizationConfig createFast() {
        return TTSOptimizationConfig.builder()
            .qualityLevel(2)
            .bitrate(128)
            .enablePostProcessing(false)
            .enableQualityAssessment(false)
            .threadPoolSize(8)
            .timeoutSeconds(60)
            .build();
    }
    
    /**
     * 验证配置有效性
     */
    public boolean isValid() {
        return defaultVoiceId != null && !defaultVoiceId.trim().isEmpty()
            && threadPoolSize != null && threadPoolSize > 0
            && maxSentenceLength != null && maxSentenceLength > 0
            && qualityLevel != null && qualityLevel >= 1 && qualityLevel <= 5
            && speechRate != null && speechRate >= 0.5 && speechRate <= 2.0
            && volume != null && volume >= 0 && volume <= 100
            && pitch != null && pitch >= -20 && pitch <= 20;
    }
    
    /**
     * 获取配置摘要
     */
    public String getConfigSummary() {
        return String.format("TTS配置[音色:%s, 质量:%d, 线程:%d, 超时:%ds]", 
            defaultVoiceId, qualityLevel, threadPoolSize, timeoutSeconds);
    }
}
