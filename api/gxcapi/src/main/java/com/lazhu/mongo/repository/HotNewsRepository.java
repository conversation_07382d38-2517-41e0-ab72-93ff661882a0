package com.lazhu.mongo.repository;

import com.lazhu.mongo.entity.HotNews;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
public interface HotNewsRepository extends ReactiveMongoRepository<HotNews, String> {

    @Query("{'topic': {'$regex': ?0, '$options': 'i'}}")
    Flux<HotNews> findByTopicContaining(String keyword);

    @Query("{}")
    Flux<HotNews> findAllOrderByHeatDesc(Pageable pageable);

    @Query("{'source': ?0}")
    Flux<HotNews> findBySourceOrderByHeatDesc(String source, Pageable pageable);

    @Query(value = "{}", fields = "{'source': 1}")
    Flux<HotNews> findDistinctSources();

    Mono<Long> countBySource(String source);
}